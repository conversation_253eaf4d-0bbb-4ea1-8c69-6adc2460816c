import { Readable, Writable } from "node:stream";
import type * as streamWeb from "node:stream/web";
import {
  CacheKeyOptions,
  ORG_NAME_HEADER,
  proxyV1,
  SpanLogger,
} from "@braintrust/proxy";
import {
  APISecret,
  getModelEndpointTypes,
  refreshModels,
} from "@braintrust/proxy/schema";
import { sha256 } from "../hash";
import { ORG_NAME, PROXY_ALLOW_PASSTHROUGH_CREDENTIALS } from "../env";
import { customFetchRequest } from "../custom_fetch";
import { getRedis } from "../redis";
import { BadRequestError, isEmpty } from "../util";
import { extractAllowedOrigin, ORIGIN_HEADER } from "../cors";
import { parseBraintrustAuthHeader } from "../auth-header";
import {
  decryptMessage,
  encryptedMessageSchema,
  encryptMessage,
  isTempCredential,
  verifyTempCredentials,
} from "@braintrust/proxy/utils";
import { BT_PARENT, resolveParentHeader } from "@braintrust/core";
import { cachedLogin } from "./functions";
import { Span, startSpan } from "braintrust";
import { replacePayloadWithAttachments } from "./attachment-wrapper";
import { PENDING_FLUSHABLES } from "../pending_flushables";
import { AttachmentReference } from "@braintrust/core/typespecs";
import { getLogger } from "../instrumentation/logger";

export type GetResFn = () => Writable;

function makeFetchApiSecrets(appOrigin: string) {
  return async (
    useCache: boolean,
    authToken: string,
    model: string | null,
    org_name?: string,
  ): Promise<APISecret[]> => {
    // First try to decode & verify as JWT. We gate this on Braintrust JWT
    // format, not just any JWT, in case a future model provider uses JWT as
    // the auth token.
    if (isTempCredential(authToken)) {
      try {
        const { jwtPayload, credentialCacheValue } =
          await verifyTempCredentials({
            jwt: authToken,
            cacheGet: encryptedGet,
          });

        // Overwrite parameters with those from JWT.
        authToken = credentialCacheValue.authToken;
        model = jwtPayload.bt.model || null;
        org_name = jwtPayload.bt.org_name || undefined;
        // Fall through to normal secrets lookup.
      } catch (error) {
        // Re-throw to filter out everything except `message`.
        getLogger().error({ error }, "Error verifying temp credentials");
        throw new Error(error instanceof Error ? error.message : undefined);
      }
    }

    const cacheKey = sha256Digest(
      `${model}/${org_name ? org_name + ":" : ""}${authToken}`,
    );

    const cacheResponse = useCache && (await encryptedGet(cacheKey, cacheKey));
    if (cacheResponse) {
      return JSON.parse(cacheResponse);
    }

    let secrets: APISecret[] = [];
    // Only cache API keys for 60 seconds. This reduces the load on the database but ensures
    // that changes roll out quickly enough too.
    const ttl = 60;
    let lookupFailed = false;
    try {
      const response = await customFetchRequest(`${appOrigin}/api/secret`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${authToken}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          model,
          org_name,
          mode: "full",
        }),
      });
      if (response.ok) {
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        secrets = (await response.body.json()) as APISecret[];
      } else {
        if (PROXY_ALLOW_PASSTHROUGH_CREDENTIALS) {
          lookupFailed = true;
        } else {
          throw new Error(
            `Failed to lookup api key: ${response.statusCode}: ${await response.body.text()}`,
          );
        }
      }
    } catch (e) {
      if (PROXY_ALLOW_PASSTHROUGH_CREDENTIALS) {
        lookupFailed = true;
        getLogger().warn(
          { error: e },
          "Failed to lookup api key. Falling back to provided key",
        );
      } else {
        throw e;
      }
    }

    // This check enforces that the secrets are only used for the org that this proxy is configured to support.
    const filteredSecrets = [];
    for (const secret of secrets) {
      if (ORG_NAME === "*" || secret.org_name === ORG_NAME) {
        filteredSecrets.push(secret);
      } else {
        getLogger().warn(
          {
            ORG_NAME,
            secret_org_name: secret.org_name,
            secret_name: secret.name,
          },
          "Filtering out secret because its org name does not match the ORG_NAME configured for this deployment",
        );
      }
    }
    if (secrets.length > 0 && filteredSecrets.length === 0) {
      throw new Error(
        "No secrets match the ORG_NAME configured for this deployment. Please see logs for more details.",
      );
    }
    secrets = filteredSecrets;

    // In the public proxy, this block of code enables using the provider's key. However, since this proxy
    // runs within the Braintrust product (and in customer clouds), we want to constrain it to authorized
    // users only.
    //
    if (lookupFailed) {
      const endpointTypes = !isEmpty(model) ? getModelEndpointTypes(model) : [];
      secrets.push({
        secret: authToken,
        type: endpointTypes[0] ?? "openai",
      });
    }

    // We don't await this, but worst case it doesn't complete before the next request.
    encryptedPut(cacheKey, cacheKey, JSON.stringify(secrets), {
      ttl,
    }).catch((e) => {
      getLogger().error({ error: e }, "Error while caching api secrets");
    });

    return secrets;
  };
}

async function encryptedCachePut(
  encryptionKey: string,
  key: string,
  value: string,
  ttl_seconds?: number,
) {
  await encryptedPut(encryptionKey, key, value, {
    // Cache it for a week if not specified
    ttl: ttl_seconds ?? 60 * 60 * 24 * 7,
  });
}

interface ProxyConfiguration {
  cacheGet?: (encryptionKey: string, key: string) => Promise<string | null>;
  cachePut?: (
    encryptionKey: string,
    key: string,
    value: string,
    ttl_seconds?: number,
  ) => Promise<void>;
  defaultHeaders?: Record<string, string>;
  digest?: (message: string) => Promise<string>;
  cacheKeyOptions?: CacheKeyOptions;
}

let proxyConfiguration = {
  cacheGet: encryptedGet,
  cachePut: encryptedCachePut,
  defaultHeaders: {},
  digest: async (message: string) => sha256Digest(message),
  cacheKeyOptions: {},
};

export function setProxyConfiguration(config: ProxyConfiguration) {
  proxyConfiguration = {
    ...proxyConfiguration,
    ...config,
  };
}

export interface ProxyArgs {
  method: "GET" | "POST";
  url: string;
  headers: Record<string, string>;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  body: any;
  res: WritableStream<Uint8Array>;
  setHeader: (name: string, value: string) => void;
  setStatusCode: (code: number) => void;
  spanLogger?: SpanLogger;
}

export async function nodeProxyV1({
  getRes,
  headers,
  setHeader,
  ...rest
}: Omit<ProxyArgs, "res"> & { getRes: GetResFn }): Promise<void> {
  const { readable, writable } = new TransformStream();

  const appOrigin = extractAllowedOrigin(headers[ORIGIN_HEADER]);
  const parentHeader = headers[BT_PARENT];
  let span: Span | undefined;
  let spanLogger: SpanLogger | undefined;
  if (parentHeader && typeof parentHeader === "string") {
    let parent;
    try {
      parent = resolveParentHeader(parentHeader);
    } catch (e) {
      throw new BadRequestError(
        `Invalid parent header ${parentHeader}: ${e instanceof Error ? e.message : `${e}`}`,
      );
    }

    const ctxToken = parseBraintrustAuthHeader(headers) ?? undefined;

    const state = await cachedLogin({
      token: ctxToken,
      appOrigin,
      orgName: headers[ORG_NAME_HEADER],
      setHeader,
    });
    span = startSpan({
      state,
      type: "llm",
      name: "LLM",
      parent: parent.toStr(),
    });
    spanLogger = makeProxySpanLogger(span, {});
  }

  // This is a cheap no-op if already run within the cache period (1 hour).
  await refreshModels(appOrigin);

  // Note: we must resolve the proxy after forwarding the stream to `res`,
  // because the proxy promise resolves after its internal stream has finished
  // writing.
  try {
    await proxyWrapper({
      res: writable,
      headers,
      setHeader,
      spanLogger,
      ...rest,
    });
  } catch (e) {
    if (span) {
      PENDING_FLUSHABLES.add(span.flush());
    }
    throw e;
  }

  const res = getRes();

  const flushedReadable = readable.pipeThrough(
    new TransformStream({
      transform(chunk, controller) {
        controller.enqueue(chunk);
      },
      flush() {
        if (span) {
          PENDING_FLUSHABLES.add(span.flush());
        }
      },
    }),
  );

  // https://stackoverflow.com/questions/73308289/typescript-error-converting-a-native-fetch-body-webstream-to-a-node-stream
  const readableNode = webReadableToNodeReadable(flushedReadable);

  readableNode.pipe(res, { end: true });
}

export async function proxyWrapper({
  method,
  url,
  headers,
  body,
  res,
  setHeader,
  setStatusCode,
  spanLogger,
}: {
  method: "GET" | "POST";
  url: string;
  headers: Record<string, string>;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  body: any;
  res: WritableStream<Uint8Array>;
  setHeader: (name: string, value: string) => void;
  setStatusCode: (code: number) => void;
  spanLogger?: SpanLogger;
}): Promise<void> {
  const authToken = parseBraintrustAuthHeader(headers);

  // Note: we must resolve the proxy after forwarding the stream to `res`,
  // because the proxy promise resolves after its internal stream has finished
  // writing.
  await proxyV1({
    method,
    url,
    proxyHeaders: {
      ...proxyConfiguration.defaultHeaders,
      ...headers,
      // If the special auth header is set, then update the header that propagates
      // through the proxy.
      ...(authToken ? { Authorization: `Bearer ${authToken}` } : {}),
    },
    body,
    setHeader,
    setStatusCode,
    res,
    getApiSecrets: makeFetchApiSecrets(
      extractAllowedOrigin(headers[ORIGIN_HEADER]),
    ),
    decompressFetch: true,
    spanLogger,
    ...proxyConfiguration,
  });
}

export async function encryptedGet(encryptionKey: string, key: string) {
  let redis;
  try {
    redis = await getRedis();
  } catch (e) {
    getLogger().error(
      { error: e },
      "Unable to connect to redis. Will skip cache",
    );
    return null;
  }
  const result = await redis.get(key);
  if (!result) {
    return null;
  }
  const message = encryptedMessageSchema.parse(JSON.parse(result));
  return (
    (await decryptMessage(encryptionKey, message.iv, message.data)) ?? null
  );
}

export async function encryptedPut(
  encryptionKey: string,
  key: string,
  value: string,
  options?: {
    ttl?: number;
    extraSetOps?: { name: string; value: string }[];
  },
) {
  options = options || {};

  let redis;
  try {
    redis = await getRedis();
  } catch (e) {
    getLogger().error(
      { error: e },
      "Unable to connect to redis. Will skip cache",
    );
    return;
  }

  const encryptedValue = JSON.stringify(
    await encryptMessage(encryptionKey, value),
  );

  await Promise.all([
    redis.set(key, encryptedValue, {
      EX: options.ttl ? Math.ceil(options.ttl) : options.ttl,
    }),
    ...(options.extraSetOps ?? []).flatMap((x) => [
      redis.sAdd(x.name, x.value),
      redis.expire(x.name, 3600),
    ]),
  ]);
}

export function sha256Digest(message: string): string {
  return sha256(message, "base64");
}

export function webReadableToNodeReadable(readable: ReadableStream): Readable {
  // https://stackoverflow.com/questions/73308289/typescript-error-converting-a-native-fetch-body-webstream-to-a-node-stream
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
  return Readable.fromWeb(readable as streamWeb.ReadableStream);
}

export function makeProxySpanLogger(
  span: Span,
  attachments: Record<string, AttachmentReference>,
): SpanLogger {
  return {
    log: (args) => {
      span.log(replacePayloadWithAttachments(args, span.state(), attachments));
    },
    end: span.end.bind(span),
    setName(name) {
      span.setAttributes({ name });
    },
    reportProgress() {
      return;
    },
  };
}
