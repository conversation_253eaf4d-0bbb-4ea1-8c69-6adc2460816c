import { customFetchRequest } from "./custom_fetch";
import { getLogger } from "./instrumentation/logger";
import { ALLOWED_ORIGIN } from "./env";
import { getRedis } from "./redis";
import {
  BadRequestError,
  REDIS_INSERT_LOGS_RESOURCE_CHECK_UNLIMITED_KEY,
  REDIS_INSERT_LOGS_RESOURCE_CHECK_FAILED_KEY,
  postDefaultHeaders,
} from "./util";
import { endpointSchemas } from "@braintrust/local/app-schema";
import { z } from "zod";
import { parseNoStrip } from "@braintrust/core";

const fullResourceCheckInputSchema =
  endpointSchemas.insert_logs_resource_check_typespecs.input;

export type ResourceCheckInput = Omit<
  z.infer<typeof fullResourceCheckInputSchema>["input"],
  "additional_org_ids" | "additional_project_ids"
>;

export async function resourceCheck({
  token,
  orgIds,
  projectIds,
  getResourceCheckInput,
  wasCachedToken,
}: {
  token: string | undefined;
  orgIds: string[];
  projectIds: string[];
  getResourceCheckInput: () => ResourceCheckInput;
  wasCachedToken: string | undefined;
}): Promise<void> {
  // Skip if we are not considering any orgs.
  if (!orgIds.length) {
    return;
  }

  const redisClient = await getRedis();
  const allKeys = await Promise.all([
    ...orgIds.map((orgId) => redisClient.get(makeIsUnlimitedKey(orgId))),
    redisClient.get(makeHasFailedKey(token)),
  ]);
  const isUnlimitedResults = allKeys.slice(0, -1);
  const hasFailedResult = allKeys.at(-1);

  if (hasFailedResult) {
    if (wasCachedToken) {
      await redisClient.set(wasCachedToken, "true", { EX: 3600 });
    }
    raiseResourceCheckException(hasFailedResult);
  } else if (isUnlimitedResults.every((x) => !!x)) {
    if (wasCachedToken) {
      await redisClient.set(wasCachedToken, "true", { EX: 3600 });
    }
    return;
  }

  const resp = await customFetchRequest(
    `${ALLOWED_ORIGIN}/api/insert-logs/resource-check`,
    {
      method: "POST",
      headers: postDefaultHeaders({ token }),
      body: JSON.stringify(
        parseNoStrip(fullResourceCheckInputSchema, {
          input: {
            ...getResourceCheckInput(),
            additional_org_ids: orgIds,
            additional_project_ids: projectIds,
          },
          num_shards: 10,
        }),
      ),
    },
  );
  if (resp.ok) {
    const { is_unlimited } =
      endpointSchemas.insert_logs_resource_check_typespecs.output.parse(
        await resp.body.json(),
      );
    const unlimitedOrgs = Object.entries(is_unlimited)
      .filter(([_orgId, isUnlimited]) => isUnlimited)
      .map(([k, _v]) => k);
    await Promise.all(
      unlimitedOrgs.map((orgId) =>
        redisClient.set(makeIsUnlimitedKey(orgId), "1", { EX: 3600 }),
      ),
    );
  } else {
    const respText = await resp.body.text();
    if (isResourceViolation(respText)) {
      await redisClient.set(makeHasFailedKey(token), respText, { EX: 60 });
    }
    raiseResourceCheckException(respText);
  }
}

function makeIsUnlimitedKey(orgId: string): string {
  return [REDIS_INSERT_LOGS_RESOURCE_CHECK_UNLIMITED_KEY, orgId].join(":");
}

// Note that a resource check failure is keyed by auth token, not org or object
// ID or anything more granular. It is challenging to tie the resource check to
// anything more granular than org, because determining which objects trigger
// which resource limits is nontrivial. We could have the resource check report
// exactly which orgs had their resource constraints violated, but in practice,
// since most API keys are org-scoped anyways, we hope this will not be
// necessary.
//
// In the worst case, if a user hits a resource limit inserting into one org,
// they can wait 60 seconds and then start inserting into the other orgs they
// are a part of. We can always revisit this logic in the future if the need
// arises.
function makeHasFailedKey(token: string | undefined): string {
  return [REDIS_INSERT_LOGS_RESOURCE_CHECK_FAILED_KEY, token ?? "anon"].join(
    ":",
  );
}

function isResourceViolation(respText: string) {
  return respText.includes("Violations of resource constraint");
}

export class ResourceCheckException extends BadRequestError {
  constructor(respText: string) {
    super(respText);
  }
}

function raiseResourceCheckException(respText: string) {
  const errmsg = (() => {
    if (!isResourceViolation(respText)) {
      return respText;
    }
    return `You have reached your plan limits. Please visit https://www.braintrust.dev/app/settings?subroute=billing to manage your plans, or contact <NAME_EMAIL> if you believe this is a mistake.

Full debug details:
${respText}`;
  })();
  getLogger().debug({ respText }, "Resource check failed");
  throw new ResourceCheckException(errmsg);
}
