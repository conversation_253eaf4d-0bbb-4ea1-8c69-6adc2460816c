"use client";

import { useUser as useClerkUser, useReverification } from "@clerk/nextjs";
import { Button } from "#/ui/button";
import { AddMfa } from "./add";
import { type TOTPResource, type Clerk<PERSON>IError } from "@clerk/types";
import { ClerkErrors, handleClerkError } from "./errors";
import { BackupCodes } from "./backup-codes";
import { useState } from "react";
import { CheckCircle } from "lucide-react";
import { PasswordSection } from "./password-section";

function TotpEnabled() {
  const { user } = useClerkUser();
  const disableTOTP = useReverification(() => user?.disableTOTP());
  return (
    <>
      <div className="flex items-center gap-2 pb-3 pt-2 text-sm">
        <CheckCircle className="size-3" />
        An authenticator app has been enabled for your account
      </div>
      <Button size="sm" onClick={disableTOTP}>
        Remove
      </Button>
      <BackupCodes />
    </>
  );
}

function TotpDisabled() {
  const { user } = useClerkUser();
  const createTOTP = useReverification(() => user?.createTOTP());
  const [totp, setTOTP] = useState<TOTPResource | undefined>(undefined);
  const [errors, setErrors] = useState<ClerkAPIError[]>();

  if (totp) {
    return <AddMfa totp={totp} />;
  }

  return (
    <>
      <Button
        onClick={async () => {
          try {
            const totp = await createTOTP();
            setTOTP(totp);
          } catch (error) {
            setErrors(handleClerkError(error));
          }
        }}
      >
        Setup authenticator app
      </Button>
      {errors && <ClerkErrors errors={errors} />}
    </>
  );
}

export default function ManageMFA() {
  const { user } = useClerkUser();

  if (!user) {
    return null;
  }

  return (
    <>
      <h2 className="mb-2 text-lg font-semibold">Security</h2>
      <div className="mb-4 text-sm text-primary-600">
        Manage your account security settings
      </div>

      <div className="space-y-8">
        <PasswordSection />

        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <CheckCircle className="size-4" />
            <h3 className="text-base font-medium">Two-Factor Authentication</h3>
          </div>
          <div className="text-sm text-primary-600">
            Configure two-factor authentication to verify identity
          </div>
          {user.totpEnabled ? <TotpEnabled /> : <TotpDisabled />}
        </div>
      </div>
    </>
  );
}
