"use client";

import { useState } from "react";
import { useUser as useClerkUser } from "@clerk/nextjs";
import { Button } from "#/ui/button";
import { PasswordModal } from "./password-modal";
import { Lock } from "lucide-react";

export function PasswordSection() {
  const { user } = useClerkUser();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState<"set" | "update">("set");

  if (!user) {
    return null;
  }

  const hasPassword = user.passwordEnabled;

  const handleOpenModal = (mode: "set" | "update") => {
    setModalMode(mode);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <Lock className="size-4" />
        <h3 className="text-base font-medium">Password</h3>
      </div>

      {hasPassword ? (
        <div className="space-y-3">
          <div className="flex items-center gap-2 pb-3 pt-2 text-sm">
            <span>Current password:</span>
            <span className="font-mono">••••••••</span>
          </div>
          <Button size="sm" onClick={() => handleOpenModal("update")}>
            Update password
          </Button>
        </div>
      ) : (
        <div className="space-y-3">
          <div className="text-sm text-primary-600">
            No password has been set for your account
          </div>
          <Button size="sm" onClick={() => handleOpenModal("set")}>
            Set password
          </Button>
        </div>
      )}

      <PasswordModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        mode={modalMode}
      />
    </div>
  );
}
