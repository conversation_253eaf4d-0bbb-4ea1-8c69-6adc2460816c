import { getAvailableModels } from "@braintrust/proxy/schema";
import {
  DropdownMenuCheckboxItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
} from "#/ui/dropdown-menu";
import { But<PERSON> } from "#/ui/button";
import { Check, CircleAlert, Plus } from "lucide-react";
import { ModelOptionLabel } from "./model-icon";
import {
  providerReadableName,
  getProviderIcon,
} from "../../settings/secrets/utils";
import Link from "next/link";
import { type ModelDetails } from "#/ui/prompts/models";
import {
  BasicTooltip,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "#/ui/tooltip";
import { AIProviderLogoStack } from "#/ui/prompts/empty";
import { cn } from "#/utils/classnames";
import { TooltipPortal } from "@radix-ui/react-tooltip";
import { NestedDropdown } from "#/ui/nested-dropdown";
import { forwardRef, memo, useState, type HTMLAttributes } from "react";
import { ModelOptionTooltip } from "./model-option-tooltip";
import { useUpsellContext } from "#/app/playground/upsell-dialog";

type ModelItem = ModelDetails & { provider: string };

function filterModels(modelQuery: string, models?: ModelItem[]) {
  return models
    ? models.filter(
        (m) =>
          m.modelName.toLocaleLowerCase().includes(modelQuery.toLowerCase()) ||
          (m.displayName &&
            m.displayName
              .toLocaleLowerCase()
              .includes(modelQuery.toLowerCase())),
      )
    : [];
}

export const ModelDropdown = memo(
  ({
    isReadOnly,
    orgName,
    modelOptionsByProvider,
    currentModel,
    onChange,
    currentModelNotFound,
    isLoading,
    className,
    isInSubMenu,
    hideAddProviders = false,
  }: {
    isReadOnly?: boolean;
    orgName: string;
    modelOptionsByProvider: Record<string, ModelDetails[]>;
    currentModel?: string;
    onChange: (value: string) => void;
    currentModelNotFound?: boolean;
    isLoading?: boolean;
    className?: string;
    isInSubMenu?: boolean;
    hideAddProviders?: boolean;
  }) => {
    const [open, setOpen] = useState(false);
    const currentModelEntry = Object.values(modelOptionsByProvider)
      .flatMap((x) => x)
      .find((m) => m.modelName === currentModel);

    const ModelMenuItem = forwardRef<
      HTMLDivElement,
      { item: ModelItem; isLeaf?: boolean } & HTMLAttributes<HTMLDivElement>
    >(({ item: model, isLeaf, ...rest }, ref) => {
      const checkedChild = model.children?.find(
        (c) => c.modelName === currentModel,
      );
      const checked =
        model.modelName === currentModel || (!!checkedChild && !isLeaf);
      if (model.deprecated && !checked) {
        return null;
      }
      const availableChildren =
        model.children?.filter(
          (model) => model === checkedChild || !model.deprecated,
        ) || [];
      if (availableChildren.length > 0 && !isLeaf) {
        return (
          <DropdownMenuSub>
            <DropdownMenuSubTrigger
              onClick={() => {
                onChange(model.modelName);
                setOpen(false);
              }}
            >
              <Check
                className={cn("size-3 opacity-0", {
                  "opacity-100": checked,
                })}
              />
              <ModelOptionLabel
                model={model.modelName}
                displayName={model.displayName}
                deprecated={model.deprecated}
                experimental={model.experimental}
              />
            </DropdownMenuSubTrigger>
            <DropdownMenuSubContent>
              <DropdownMenuLabel>Base model</DropdownMenuLabel>
              <ModelMenuItem
                isLeaf
                item={{ ...model, provider: model.provider }}
              />
              <DropdownMenuSeparator />
              <DropdownMenuLabel>Variations</DropdownMenuLabel>
              {availableChildren.map((c) => (
                <ModelMenuItem
                  isLeaf
                  key={c.modelName}
                  item={{ ...c, provider: model.provider }}
                />
              ))}
            </DropdownMenuSubContent>
          </DropdownMenuSub>
        );
      }
      return (
        <BasicTooltip
          side="right"
          className="rounded-md"
          tooltipContent={<ModelOptionTooltip model={model} />}
        >
          <DropdownMenuCheckboxItem
            {...rest}
            ref={ref}
            onSelect={() => onChange(model.modelName)}
            checked={checked}
          >
            <ModelOptionLabel
              model={model.modelName}
              displayName={model.displayName}
              deprecated={model.deprecated}
              experimental={model.experimental}
            />
          </DropdownMenuCheckboxItem>
        </BasicTooltip>
      );
    });
    ModelMenuItem.displayName = "ModelMenuItem";

    const { onUpsell } = useUpsellContext();

    return (
      <NestedDropdown<ModelItem>
        open={open}
        isInSubMenu={isInSubMenu}
        //When read only, we don't want the dropdown to be opened on click.
        setOpen={isReadOnly ? () => {} : setOpen}
        objectType="model"
        renderGroupLabel={(label, nested) => (
          <span className={`"w-full flex flex-1 items-center gap-2`}>
            {!nested && getProviderIcon(label, 16)}
            <span className="flex-1">{providerReadableName(label)}</span>
          </span>
        )}
        subGroups={Object.entries(modelOptionsByProvider).map(
          ([groupLabel, items]) => ({
            groupLabel,
            items: items.map((i) => ({ ...i, provider: groupLabel })),
          }),
        )}
        DropdownItemComponent={ModelMenuItem}
        filterItems={filterModels}
        additionalActions={
          hideAddProviders
            ? []
            : [
                {
                  // Link to model settings if not in upsell mode, otherwise show the upsell modal and link nowhere.
                  label: (
                    <Link
                      href={onUpsell ? "#" : `/app/${orgName}/settings/secrets`}
                    >
                      <Plus size={14} />
                      <div className="flex flex-1 items-center justify-between">
                        <span className="mr-2 text-xs">Add AI providers</span>
                        <AIProviderLogoStack
                          iconSize={14}
                          iconClassName="size-5 -mr-1.5"
                        />
                      </div>
                    </Link>
                  ),
                  onSelect: onUpsell,
                },
              ]
        }
      >
        <Button
          className={cn(
            "flex-1 justify-start border px-2 py-1.5 text-xs disabled:opacity-100 overflow-hidden",
            {
              "rounded-r-none": !currentModelNotFound && !!currentModel,
            },
            className,
          )}
          disabled={isReadOnly}
          variant="ghost"
          isDropdown={!isReadOnly}
          isLoading={isLoading}
        >
          {currentModel ? (
            <span className="flex flex-1 flex-row truncate text-left">
              <ModelOptionLabel
                model={currentModel}
                displayName={
                  currentModelEntry?.displayName ??
                  getAvailableModels()[currentModel]?.displayName ??
                  currentModel
                }
                modelNotFound={currentModelNotFound}
                labelClassName="truncate"
              />
            </span>
          ) : (
            <span className="flex-1 text-left font-normal text-primary-500">
              Select model
            </span>
          )}
          {currentModelNotFound && (
            <Tooltip>
              <TooltipTrigger>
                <CircleAlert className="ml-2 size-3 text-bad-600" />
              </TooltipTrigger>
              <TooltipPortal>
                <TooltipContent className="text-xs">
                  This model could not be found.{" "}
                  {!isReadOnly && (
                    <>
                      Select a different model or check the configuration in{" "}
                      <Link
                        href={`/app/${orgName}/settings/secrets`}
                        className="font-medium text-accent-500"
                      >
                        settings
                      </Link>
                      .
                    </>
                  )}
                </TooltipContent>
              </TooltipPortal>
            </Tooltip>
          )}
        </Button>
      </NestedDropdown>
    );
  },
);
ModelDropdown.displayName = "ModelDropdown";
