import { useEntityStorage } from "#/lib/clientDataStorage";
import { useSessionToken } from "#/utils/auth/session-token";
import { useOrg } from "#/utils/user";
import {
  useLoadViews,
  useViewOperations,
  type View,
  type ViewParams,
} from "#/utils/view/use-view-generic";
import {
  monitorViewOptionsSchema,
  type MonitorViewOptions,
} from "@braintrust/core/typespecs";
import { useSearchParams } from "next/navigation";
import {
  parseAsBoolean,
  parseAsString,
  parseAsStringLiteral,
  useQueryState,
} from "nuqs";
import { type ProjectSummary } from "../org-actions";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { type UrlSearch, urlSearchSchema } from "#/utils/search/search";
import { parseAsJsonEncoded } from "#/ui/query-parameters";
import { DEFAULT_TIME_RANGE } from "./time-controls/time-range";
import { useViewsQueryKey } from "#/utils/view/use-view";
import { getObjValueByPath } from "@braintrust/core";

type MonitorOptionsWithSearch = MonitorViewOptions & {
  search?: UrlSearch;
};

const viewTypeEnum = monitorViewOptionsSchema.shape.type
  .unwrap()
  .unwrap().options;

const DEFAULT_VIEW: MonitorOptionsWithSearch = {
  spanType: "range",
  rangeValue: DEFAULT_TIME_RANGE.value,
  frameStart: null,
  frameEnd: null,
  tzUTC: false,
  groupBy: "",
  search: {},
};

const OPTION_PARAM_KEYS = new Set([
  "spanType",
  "rangeValue",
  "frameStart",
  "frameEnd",
  "tzUTC",
  "groupBy",
  "search",
]);

function isParamKey(value: string): value is keyof MonitorOptionsWithSearch {
  return OPTION_PARAM_KEYS.has(value);
}

// create string key on the options we save
const getViewOptionsKey = (opts: MonitorOptionsWithSearch) => {
  // Normalize search options because some attributes do not effect query state
  // and end up in search options that we do want to compare.
  // Like originType and label, which are inconsistently are serialized
  const normSearch = {
    filter:
      opts.search?.filter?.map((f) => (typeof f === "string" ? f : f.text)) ??
      [],
    match:
      opts.search?.match?.map((m) => (typeof m === "string" ? m : m.text)) ??
      [],
  };

  const relevantOptions = {
    spanType: opts.spanType ?? DEFAULT_VIEW.spanType,
    rangeValue: opts.rangeValue ?? DEFAULT_VIEW.rangeValue,
    frameStart: opts.frameStart ?? "",
    frameEnd: opts.frameEnd ?? "",
    tzUTC: opts.tzUTC ?? false,
    groupBy: opts.groupBy ?? "",
    search: normSearch,
  };

  return JSON.stringify(relevantOptions);
};

export const useMonitorView = (projects: ProjectSummary[]) => {
  const { getOrRefreshToken } = useSessionToken();
  const { id: orgId, name: orgName, api_url: apiUrl } = useOrg();

  const [projectId, setProjectId] = useQueryState("projectId", parseAsString);

  const [projectType, setProjectType] = useQueryState(
    "rowType",
    parseAsStringLiteral(viewTypeEnum).withDefault("project"),
  );

  // Track if we've done the initial load
  const hasInitializedView = useRef(false);

  // "local" view states, these are our source of truth and tied to url params
  const [viewParam, setViewParam] = useQueryState("v", parseAsString);

  const [spanType, setSpanType] = useQueryState(
    "timeType",
    monitorViewOptionsSchema.shape.spanType,
  );
  const [rangeValue, setRangeValue] = useQueryState(
    "timeRange",
    monitorViewOptionsSchema.shape.rangeValue,
  );
  const [frameStart, setFrameStart] = useQueryState(
    "frameStart",
    monitorViewOptionsSchema.shape.frameStart,
  );
  const [frameEnd, setFrameEnd] = useQueryState(
    "frameEnd",
    monitorViewOptionsSchema.shape.frameEnd,
  );
  const [tzUTC, setTzUTC] = useQueryState(
    "tzUTC",
    parseAsBoolean.withDefault(false),
  );
  const [groupBy, setGroupBy] = useQueryState(
    "groupBy",
    parseAsString.withDefault(""),
  );
  const [search, setSearch] = useQueryState(
    "search",
    parseAsJsonEncoded(urlSearchSchema.parse).withDefault({}),
  );

  const searchParams = useSearchParams();

  const viewParams: ViewParams = useMemo(
    () => ({
      objectType: "org_project",
      objectId: orgId ?? "", //TODO: Handle case where orgId is not set
      viewType: "monitor",
    }),
    [orgId],
  );

  const pageIdentifier = `org-monitor-${orgId}`;
  const queryKey = useViewsQueryKey({
    pageIdentifier,
    getViewArgs: {
      apiUrl,
      getOrRefreshToken,
      viewParams,
    },
  });

  const getViewsArgs = useMemo(
    () => ({
      apiUrl,
      getOrRefreshToken,
      viewParams,
    }),
    [apiUrl, getOrRefreshToken, viewParams],
  );

  const localViewOptions: MonitorOptionsWithSearch = useMemo(
    () => ({
      spanType,
      rangeValue,
      frameStart,
      frameEnd,
      tzUTC,
      projectId,
      type: projectType,
      groupBy,
      search,
    }),
    [
      spanType,
      rangeValue,
      frameStart,
      frameEnd,
      tzUTC,
      groupBy,
      search,
      projectId,
      projectType,
    ],
  );

  const [loadedViewOptions, setLoadedViewOptions] =
    useState<MonitorOptionsWithSearch | null>();

  const loadView = useCallback(
    (view: View | null, overrides: Partial<MonitorOptionsWithSearch> = {}) => {
      if (view?.options) {
        // This page should only receive monitor views
        if ("viewType" in view.options && view.options.viewType === "monitor") {
          const optsWithSearch = {
            ...view.options.options,
            search: view?.view_data?.search,
            ...overrides,
          };
          // @ts-ignore search view isn't typed correctly
          setLoadedViewOptions(optsWithSearch);
        } else {
          console.warn("Received non-monitor view on monitor page:", view);
        }
      } else {
        // else use default view options
        setLoadedViewOptions({ ...DEFAULT_VIEW, ...overrides });
      }

      if (view?.builtin) {
        setViewParam(null);
      } else {
        setViewParam(view?.name ?? null);
      }
    },
    [setViewParam],
  );

  const {
    isUpdating: isUpdatingViews,
    createViewAsync,
    updateView,
    deleteView,
  } = useViewOperations({
    queryKey,
    viewParams,
    getViewsArgs,
    loadView,
  });

  const { data: views, isPending: isPendingViews } = useLoadViews({
    queryKey,
    viewParams,
    getViewsArgs,
    enabled: true,
  });

  const allDataView = useMemo(
    () => ({ id: null, builtin: true, name: "All data" }),
    [],
  );
  const allViews = useMemo(
    () => [
      allDataView,
      ...(views ?? []).filter((v) => {
        return (
          !projectId ||
          (isOrgProjectObjectType(v) &&
            getObjValueByPath(v, ["options", "options", "type"]) ===
              projectType &&
            getObjValueByPath(v, ["options", "options", "projectId"]) ===
              projectId)
        );
      }),
    ],
    [allDataView, views, projectId, projectType],
  );

  const selectedView = useMemo(
    () => allViews.find((v) => v.name === viewParam) ?? allDataView,
    [allViews, viewParam, allDataView],
  );

  // Get the user's default view preference from localStorage
  const [defaultViewName] = useEntityStorage({
    entityType: "tables",
    entityIdentifier: pageIdentifier,
    key: "defaultView",
  });

  const isViewDirty = useMemo(() => {
    let viewOptions = DEFAULT_VIEW;
    if ("options" in selectedView) {
      const opts = selectedView.options;
      if (opts && "viewType" in opts && opts.viewType === "monitor") {
        // @ts-ignore search view isn't typed correctly
        const search: UrlSearch | undefined = selectedView?.view_data?.search;
        viewOptions = { ...opts.options, search };
      }
    }

    const viewKey = getViewOptionsKey(viewOptions);
    const currentKey = getViewOptionsKey(localViewOptions);

    return viewKey !== currentKey;
  }, [localViewOptions, selectedView]);

  // Handle view parameter loading on page initialization
  useEffect(() => {
    if (isPendingViews || hasInitializedView.current) {
      return;
    }
    hasInitializedView.current = true;

    // use the initial url params state as override options
    // if they exist in the search params
    const overrides = !searchParams
      ? {}
      : Object.fromEntries(
          Array.from(searchParams.entries())
            .filter(([key, _]) => isParamKey(key))
            .map(([key, _]) => [
              key,
              isParamKey(key) ? localViewOptions[key] : undefined,
            ]),
        );

    // Handle case where URL parameter exists
    if (viewParam !== null) {
      const viewToLoad = allViews.find((v) => v.name === viewParam);
      if (viewToLoad) {
        loadView(viewToLoad, overrides);
      } else {
        // View doesn't exist, clear URL and load builtin view
        setViewParam(null);
        loadView(null, overrides);
      }
      return;
    }

    // No URL view parameter - check for other URL params and default view
    const hasAnyUrlParams = searchParams && searchParams.toString().length > 0;
    if (!hasAnyUrlParams && defaultViewName) {
      const defaultViewToLoad = allViews.find(
        (v) => v.name === defaultViewName,
      );
      loadView(defaultViewToLoad || null, overrides);
    } else {
      loadView(null, overrides);
    }
  }, [
    viewParam,
    isPendingViews,
    searchParams,
    allViews,
    loadView,
    setViewParam,
    allDataView,
    defaultViewName,
    localViewOptions,
  ]);

  // update local states when loaded view options change
  useEffect(() => {
    if (!loadedViewOptions) {
      return;
    }

    setSpanType(loadedViewOptions.spanType ?? null);
    setRangeValue(loadedViewOptions.rangeValue ?? null);
    setFrameStart(loadedViewOptions.frameStart ?? null);
    setFrameEnd(loadedViewOptions.frameEnd ?? null);
    setTzUTC(loadedViewOptions.tzUTC ?? null);
    setGroupBy(loadedViewOptions.groupBy ?? null);
    setSearch(loadedViewOptions.search ?? null);
    // Theoretically every visible view is related to whatever project is currently set,
    // so we don't need to set the project id and project type here
  }, [
    loadedViewOptions,
    setFrameEnd,
    setFrameStart,
    setGroupBy,
    setRangeValue,
    setSearch,
    setSpanType,
    setTzUTC,
  ]);

  // set a default projectId
  const [recentProjectIds] = useEntityStorage({
    entityType: "org",
    entityIdentifier: orgName,
    key: "recentProjectIds",
  });

  useEffect(() => {
    // do nothing if already exists
    if (projectId) {
      return;
    }

    // use most recent project if it exists
    if (recentProjectIds.length) {
      setProjectId(recentProjectIds[0]);
      return;
    }

    // else we initialize to first in projects list
    setProjectId(projects?.[0]?.project_id);
  }, [projectId, recentProjectIds, projects, setProjectId]);

  const renameView = useCallback(
    ({ name }: { name: string }) => {
      const { search, ...options } = localViewOptions;

      updateView({
        viewId: selectedView.id!,
        name,
        viewData: { search },
        options: {
          viewType: "monitor",
          options,
        },
      });
      setViewParam(name);
    },
    [updateView, selectedView.id, localViewOptions, setViewParam],
  );

  const createView = useCallback(
    async (name: string) => {
      const { search, ...options } = localViewOptions;
      const newView = await createViewAsync({
        name,
        viewData: { search },
        options: {
          viewType: "monitor",
          options,
        },
      });

      // Switch to the newly created view
      if (newView) {
        loadView(newView);
      }
      return newView;
    },
    [createViewAsync, localViewOptions, loadView],
  );

  const saveView = useCallback(() => {
    const { search, ...options } = localViewOptions;
    updateView({
      viewId: selectedView.id!,
      viewData: { search },
      options: {
        viewType: "monitor",
        options,
      },
    });
  }, [updateView, selectedView.id, localViewOptions]);

  return {
    isPendingViews,
    isUpdatingViews,
    createViewAsync,
    deleteView,

    allViews,
    selectedView,
    selectedViewName: viewParam,

    loadedViewOptions, // only for time controls

    pageIdentifier,
    loadView,
    renameView,
    createView,
    saveView,
    isViewDirty,

    // view options state and setters
    projectId,
    setProjectId,
    projectType,
    setProjectType,
    spanType,
    setSpanType,
    rangeValue,
    setRangeValue,
    frameStart,
    setFrameStart,
    frameEnd,
    setFrameEnd,
    tzUTC,
    setTzUTC,
    groupBy,
    setGroupBy,
    search,
    setSearch,
  };
};

function isOrgProjectObjectType(view: View) {
  return view && !("builtin" in view) && view.object_type === "org_project";
}
