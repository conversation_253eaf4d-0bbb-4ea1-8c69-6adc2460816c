import { useEffect, useMemo } from "react";
import {
  getAvailableModels,
  ModelSchema,
  ModelEndpointType,
  type ModelSpec,
  getModelEndpointTypes,
  AISecretTypes,
  modelProviderHasReasoning,
  CloudSecretTypes,
} from "@braintrust/proxy/schema";
import { type AISecret } from "@braintrust/core/typespecs";
import { type queryPromptModels } from "./prompt-actions";
import { useQueryFunc } from "#/utils/react-query";
import { z, ZodError } from "zod";
import { getWindowAIModel } from "#/utils/ai/window-ai";
import { Field, Float64, Schema, Utf8 } from "apache-arrow";
import { atom, useAtom } from "jotai";

export function resolveModelFromName(
  model: string,
  allAvailableModels: { [name: string]: ModelSpec },
): {
  model?: string;
  spec?: ModelSpec;
} {
  const specFromName = allAvailableModels[model];
  if (specFromName) {
    return {
      model,
      spec: specFromName,
    };
  }
  const specFromDisplayName = Object.entries(allAvailableModels).find(
    ([_, spec]) => spec.displayName === model,
  );
  return {
    model: specFromDisplayName?.[0],
    spec: specFromDisplayName?.[1],
  };
}

const modelEndpointTypeSchema = z.enum(ModelEndpointType);

export type ModelDetails = ModelSpec & {
  modelName: string;
  children?: ModelDetails[];
};

export const modelsByProvider = (() => {
  const availableModels = getAvailableModels();
  return Object.entries(availableModels).reduce(
    (acc: Record<string, ModelDetails[]>, [k, v]) => {
      // Skip models that are children (they'll be added to their parent's children array)
      if (v.parent) {
        return acc;
      }

      for (const t of getModelEndpointTypes(k)) {
        // Find all child models for this model
        const children = Object.entries(availableModels)
          .filter(([_, model]) => model.parent === k)
          .map(([childName, childSpec]) => ({
            ...childSpec,
            modelName: childName,
          }));

        acc[t] = (acc[t] ?? []).concat({
          ...v,
          modelName: k,
          children: children.length > 0 ? children : undefined,
        });
      }
      return acc;
    },
    {},
  );
})();

export type ModelCosts = {
  input_cost_per_mil_tokens?: number;
  input_cache_read_cost_per_mil_tokens?: number;
  input_cache_write_cost_per_mil_tokens?: number;
  output_cost_per_mil_tokens?: number;
};

const UNINITIALIZED = Symbol("uninitialized");
const windowAIModelAtom = atom<ModelSpec | null | typeof UNINITIALIZED>(
  UNINITIALIZED,
);

function useAllAvailableModels(apiSecrets: AISecret[] | undefined) {
  const [windowAIModel, setWindowAIModel] = useAtom(windowAIModelAtom);
  useEffect(() => {
    if (windowAIModel !== UNINITIALIZED) return;
    (async () => {
      setWindowAIModel(await getWindowAIModel());
    })();
  }, [windowAIModel, setWindowAIModel]);

  return useMemo(
    () =>
      calculateAvailableModels(
        apiSecrets,
        windowAIModel === UNINITIALIZED ? null : windowAIModel,
      ),
    [apiSecrets, windowAIModel],
  );
}

export function useAvailableModelsWithServerData({
  serverData,
}: {
  serverData: AISecret[];
}) {
  return useAllAvailableModels(serverData);
}

export function useAvailableModels({ orgName }: { orgName: string }) {
  const { data: apiSecrets, invalidate: refresh } = useQueryFunc<
    typeof queryPromptModels
  >({
    fName: "queryPromptModels",
    args: useMemo(() => ({ org_name: orgName }), [orgName]),
  });

  const availableModels = useAllAvailableModels(apiSecrets);
  return { ...availableModels, refresh };
}

export const modelArrowSchema: Schema = new Schema([
  Field.new({ name: "model", type: new Utf8() }),
  Field.new({ name: "input_cost_per_mil_tokens", type: new Float64() }),
  Field.new({
    name: "input_cache_read_cost_per_mil_tokens",
    type: new Float64(),
  }),
  Field.new({
    name: "input_cache_write_cost_per_mil_tokens",
    type: new Float64(),
  }),
  Field.new({ name: "output_cost_per_mil_tokens", type: new Float64() }),
]);

export const modelTableDefinition = `(
  model string,
  input_cost_per_mil_tokens double,
  input_cache_read_cost_per_mil_tokens double,
  input_cache_write_cost_per_mil_tokens double,
  output_cost_per_mil_tokens double
)`;

type CustomModelError = { secretName: string; error: string };

function calculateAvailableModels(
  apiSecrets: AISecret[] | undefined,
  windowAIModel: ModelSpec | null,
) {
  const apiSecretsByType = (apiSecrets ?? []).reduce(
    (acc: Record<string, string[]>, s) => ({
      ...acc,
      [s.type ?? ""]: (acc[s.type ?? ""] ?? []).concat(s.name),
    }),
    {},
  );

  const customModelErrors: CustomModelError[] = [];
  const customModels: { [name: string]: ModelSpec } = Object.fromEntries(
    apiSecrets?.flatMap((s) => {
      let customModels:
        | Record<string, z.infer<typeof ModelSchema>>
        | null
        | undefined = null;
      try {
        customModels = z
          .record(ModelSchema)
          .nullish()
          .parse(s.metadata?.customModels);
      } catch (e) {
        if (e instanceof ZodError) {
          customModelErrors.push({
            secretName: s.name,
            error: e.errors
              .map((err) => `• ${err.path.join(".")}: ${err.message}`)
              .join("\n"),
          });
        } else {
          customModelErrors.push({
            secretName: s.name,
            error: e instanceof Error ? e.message : String(e),
          });
        }
        customModels = null;
      }
      return Object.entries(customModels ?? {}).map(([name, spec]) => [
        name,
        {
          ...spec,
          // TODO: move to the API?
          reasoning:
            spec.reasoning ||
            spec.o1_like ||
            modelProviderHasReasoning?.[spec.format]?.test(name),
        },
      ]);
    }) ?? [],
  );

  // If customModels includes a model present in AvailableModels, we want the custom spec to take precedence
  // over the hardcoded spec, so concatenate the customModels to the AvailableModels.
  const allAvailableModels: { [name: string]: ModelSpec } = Object.fromEntries(
    Object.entries(getAvailableModels())
      .concat(Object.entries(customModels))
      .concat(windowAIModel ? [["window", windowAIModel]] : []),
  );

  let hasNontrivialModel = false;
  Object.keys(allAvailableModels).forEach((m) =>
    getModelEndpointTypes(m).forEach((t) => {
      hasNontrivialModel ||= t !== "js" && apiSecretsByType[t]?.length > 0;
    }),
  );

  const allAvailableModelCosts = Object.entries({
    ...getAvailableModels(),
    ...customModels,
  }).reduce(
    (
      acc: {
        [key: string]: {
          input_cost_per_mil_tokens?: number;
          input_cache_read_cost_per_mil_tokens?: number;
          input_cache_write_cost_per_mil_tokens?: number;
          output_cost_per_mil_tokens?: number;
        };
      },
      [modelName, modelSpec],
    ) => {
      const {
        input_cost_per_mil_tokens,
        output_cost_per_mil_tokens,
        input_cache_read_cost_per_mil_tokens,
        input_cache_write_cost_per_mil_tokens,
        input_cost_per_token,
        output_cost_per_token,
      } = modelSpec;
      if (
        !input_cost_per_mil_tokens &&
        !output_cost_per_mil_tokens &&
        !input_cache_read_cost_per_mil_tokens &&
        !input_cache_write_cost_per_mil_tokens &&
        !input_cost_per_token &&
        !output_cost_per_token
      ) {
        return acc;
      }
      acc[modelName] = {
        input_cost_per_mil_tokens:
          input_cost_per_mil_tokens ??
          (input_cost_per_token && input_cost_per_token * 1000000) ??
          undefined,
        input_cache_read_cost_per_mil_tokens:
          input_cache_read_cost_per_mil_tokens ??
          (input_cost_per_token && input_cost_per_token * 1000000) ??
          undefined,
        input_cache_write_cost_per_mil_tokens:
          input_cache_write_cost_per_mil_tokens ??
          (input_cost_per_token && input_cost_per_token * 1000000) ??
          undefined,
        output_cost_per_mil_tokens:
          output_cost_per_mil_tokens ??
          (output_cost_per_token && output_cost_per_token * 1000000) ??
          undefined,
      };
      return acc;
    },
    {},
  );

  const modelInserts: {
    model: string;
    input_cost_per_mil_tokens?: number | null;
    input_cache_read_cost_per_mil_tokens?: number | null;
    input_cache_write_cost_per_mil_tokens?: number | null;
    output_cost_per_mil_tokens?: number | null;
  }[] = Object.entries(allAvailableModelCosts).map(
    ([modelName, modelCosts], i) => {
      const {
        input_cost_per_mil_tokens,
        input_cache_read_cost_per_mil_tokens,
        input_cache_write_cost_per_mil_tokens,
        output_cost_per_mil_tokens,
      } = modelCosts;
      return {
        model: modelName,
        input_cost_per_mil_tokens,
        input_cache_read_cost_per_mil_tokens,
        input_cache_write_cost_per_mil_tokens,
        output_cost_per_mil_tokens,
      };
    },
  );

  const getCustomModelEntries = ({
    metadata,
    secretName,
  }: {
    metadata: Record<string, unknown> | null | undefined;
    secretName: string;
  }): { models: ModelDetails[]; errors: CustomModelError[] } => {
    if (
      !metadata?.customModels ||
      Object.keys(metadata.customModels).length === 0
    )
      return { models: [], errors: [] };
    let customModels: Record<string, z.infer<typeof ModelSchema>> = {};
    try {
      customModels = z.record(ModelSchema).parse(metadata.customModels);
    } catch (e) {
      if (e instanceof ZodError) {
        return {
          models: [],
          errors: [
            {
              secretName,
              error: e.errors
                .map((err) => `• ${err.path.join(".")}: ${err.message}`)
                .join("\n"),
            },
          ],
        };
      } else {
        return {
          models: [],
          errors: [
            {
              secretName,
              error: e instanceof Error ? e.message : String(e),
            },
          ],
        };
      }
    }
    return {
      models: Object.entries(customModels).map(([modelName, modelSpecs]) => ({
        ...modelSpecs,
        modelName,
        displayName: modelSpecs.displayName ?? modelName,
      })),
      errors: [],
    };
  };

  const configuredModelsByProvider: Record<string, ModelDetails[]> = {};
  for (const { type, name, metadata } of apiSecrets ?? []) {
    if (!type) {
      continue;
    }

    const key = !!AISecretTypes[name] || !!CloudSecretTypes[name] ? type : name;

    const customModelResult = getCustomModelEntries({
      metadata,
      secretName: name,
    });
    const models = [
      ...(metadata?.excludeDefaultModels ? [] : (modelsByProvider[type] ?? [])),
      ...customModelResult.models.map((m) => ({ ...m, type })),
    ];
    if (models.length === 0) {
      continue;
    }
    configuredModelsByProvider[key] = [
      ...(configuredModelsByProvider[key] ?? []),
      ...models.filter(
        (model) =>
          !configuredModelsByProvider[key]?.some(
            (existing) => existing.modelName === model.modelName,
          ),
      ),
    ];
  }
  if (windowAIModel) {
    configuredModelsByProvider.window = [
      { ...windowAIModel, modelName: "window" },
    ];
  }

  const sortModels = (providerA: string, providerB: string) => {
    const parsedIndexA = modelEndpointTypeSchema.safeParse(providerA);
    const parsedIndexB = modelEndpointTypeSchema.safeParse(providerB);
    const indexA = parsedIndexA.success
      ? ModelEndpointType.indexOf(parsedIndexA.data)
      : -1;
    const indexB = parsedIndexB.success
      ? ModelEndpointType.indexOf(parsedIndexB.data)
      : -1;
    const positionA = (indexA >= 0 ? indexA : ModelEndpointType.length) + 1;
    const positionB = (indexB >= 0 ? indexB : ModelEndpointType.length) + 1;

    return positionA - positionB;
  };

  const modelsByProviderSorted = Object.fromEntries(
    Object.entries(configuredModelsByProvider).sort(
      ([providerA], [providerB]) => sortModels(providerA, providerB),
    ),
  );

  return {
    allAvailableModels,
    allAvailableModelCosts,
    noConfiguredSecrets: apiSecrets !== undefined && !hasNontrivialModel,
    modelInserts,
    apiSecrets,
    configuredModelsByProvider: modelsByProviderSorted,
    modelsByProvider,
    customModelErrors,
  };
}
