import {
  type Dispatch,
  type SetStateAction,
  useEffect,
  useMemo,
  useState,
} from "react";
import { getInferField, type InferField } from "./filter-editor-infer-data";
import { Combobox } from "#/ui/combobox/combobox";
import { Input } from "#/ui/input";

export const FilterEditorTopValuesSelector = (props: {
  inferFields: InferField[];
  inferPath: string[];
  value: string;
  setValue: Dispatch<SetStateAction<string>>;
  onEnter?: () => void;
  showCounts: boolean;
  isScore: boolean;
}) => {
  const {
    inferFields,
    inferPath,
    value,
    setValue,
    onEnter,
    showCounts,
    isScore,
  } = props;

  const [showCustomField, setShowCustomField] = useState<boolean>(false);

  const inferField = useMemo(() => {
    return getInferField(inferFields, inferPath);
  }, [inferFields, inferPath]);

  const selectedTopValues = useMemo(() => {
    const rawTopValues = inferField?.topValues ?? [];

    // trim and filter values
    const filteredTopValues = rawTopValues
      .map((t) => ({ ...t, value: String(t.value).trim() }))
      .filter((t) => Boolean(t.value));

    // dedupe and add count
    const valueToCount = new Map<string, number>();
    filteredTopValues.forEach((t) => {
      valueToCount.set(t.value, (valueToCount.get(t.value) ?? 0) + t.count);
    });

    // remap and sort by
    const dedupedAndSorted = [...valueToCount.entries()]
      .map(([value, count]) => ({ value, count }))
      .toSorted((a, b) => {
        if (isScore) {
          const aVal = parseFloat(a.value);
          const bVal = parseFloat(b.value);
          if (Number.isFinite(aVal) && Number.isFinite(bVal)) {
            return bVal - aVal;
          }
          if (Number.isFinite(bVal)) {
            return 1;
          }
          if (Number.isFinite(aVal)) {
            return -1;
          }
        }
        return b.count - a.count;
      });
    return dedupedAndSorted;
  }, [inferField, isScore]);

  const options = useMemo(() => {
    return selectedTopValues.map((t) => {
      const name = t.value;
      if (isScore) {
        const score = parseFloat(name);
        if (Number.isFinite(score)) {
          const scoreValue = Math.max(0, Math.min(100, 100 * score));
          const scoreFormatted = `${scoreValue.toFixed(2)}%`;
          return {
            label: scoreFormatted,
            value: String(scoreValue),
            count: t.count,
          };
        }
      }
      return {
        label: name,
        value: name,
        count: t.count,
      };
    });
  }, [selectedTopValues, isScore]);

  const selectedOption = useMemo(() => {
    if (showCustomField) {
      return;
    }

    const existing = options.find((opt) => opt.value === value);
    if (existing) {
      return existing;
    }

    return options.at(0);
  }, [options, value, showCustomField]);

  // reset state when field changes
  useEffect(() => {
    setValue("");
    setShowCustomField(false);
  }, [inferField, setValue, setShowCustomField]);

  // if no options, show custom
  useEffect(() => {
    if (options.length === 0) {
      setShowCustomField(true);
    }
  }, [options]);

  // for defaulting initial selection
  // Note: effects are order dependent unfortunately
  useEffect(() => {
    if (showCustomField) {
      return;
    }
    if (selectedOption && selectedOption.value !== value) {
      setValue(selectedOption.value);
    }
  }, [showCustomField, selectedOption, value, inferPath, setValue]);

  return (
    <>
      {options.length >= 1 && (
        <Combobox
          options={options}
          noSearch={options.length < 5}
          variant="button"
          buttonSize="xs"
          searchPlaceholder="Search existing values"
          selectedValue={selectedOption?.value}
          placeholderLabel="Custom value"
          itemLabelClassName="line-clamp-3"
          align="start"
          onChange={(v) => {
            setShowCustomField(false);
            setValue(v ?? "");
          }}
          renderOptionLabel={(opt) => {
            if (!showCounts) {
              return opt.label;
            }
            return (
              <span className="flex w-full justify-between gap-1">
                <span>{opt.label}</span>
                <span className="right text-[10px] tabular-nums text-primary-500">
                  {opt.count}
                </span>
              </span>
            );
          }}
          bottomActions={[
            {
              label: "Use a custom value",
              onSelect: () => {
                setShowCustomField(true);
                setValue("");
              },
              selected: Boolean(showCustomField),
            },
          ]}
        />
      )}
      {showCustomField && (
        <div className="flex">
          <Input
            autoFocus
            type="text"
            placeholder="Enter value"
            name="value"
            value={value}
            onChange={(e) => {
              setValue(e.target.value);
            }}
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                onEnter?.();
              }
            }}
            className="ml-2 h-7 px-2 text-xs"
          />
        </div>
      )}
    </>
  );
};
