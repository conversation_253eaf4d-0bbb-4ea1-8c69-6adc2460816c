"use client";
import React, { useMemo } from "react";
import { TooltipContent } from "#/ui/tooltip";
import { cn } from "#/utils/classnames";
import { DateTooltipContent, isValidTimestamp } from "#/ui/date";
import { flexRender } from "@tanstack/react-table";
import { SyntaxHighlight } from "#/ui/syntax-highlighter";
import { MarkdownViewer } from "#/ui/markdown";
import { type CellContentProps } from "./cell-content";
import { useStreamingData } from "./streaming";
import { serializeJSONWithPlainString } from "#/utils/object";

// performance improvement for super long json objects
const MAX_LINES = 50;

const CellTooltipContent = <TsTable, TsValue>({
  cell,
  value,
  valueDiffObject,
  mergedValues,
  colorClassName,
  meta,
  index,
  streamingContentProps,
  isLoading,
}: Omit<CellContentProps<TsTable, TsValue>, "size" | "tableType">) => {
  const streamingContent = useStreamingData({
    colName: cell.column.columnDef.id ?? "",
    rowOriginal: cell.row.original,
    index: index ?? 0,
    streamingContentProps,
    isGridLayout: false,
  });

  const cellContext = cell.getContext();
  // not sure why this is rendering multiple times,
  // but the content of this tooltip shouldn't change
  // so we can improve performance by memoizing
  const defaultContent = useMemo(() => {
    return flexRender(cell.column.columnDef.cell, {
      inTable: true,
      renderForTooltip: (value: string | React.ReactNode) => {
        if (typeof value !== "string") {
          return serializeJSONWithPlainString(value);
        }
        let jsonString: string = "";
        try {
          jsonString = JSON.stringify(JSON.parse(value), null, 2);
        } catch {
          return (
            <MarkdownViewer
              className="whitespace-normal p-0"
              value={value.replace(/\\n/g, "\n")}
            />
          );
        }
        return (
          <SyntaxHighlight
            language="json"
            className="text-xs"
            content={jsonString.split("\n").slice(0, MAX_LINES).join("\n")}
          />
        );
      },
      meta,
      value,
      valueDiffObject,
      mergedValues,
      colorClassName,
      diffIndex: index,
      ...cellContext,
    });
  }, [
    cell,
    value,
    valueDiffObject,
    cellContext,
    meta,
    mergedValues,
    colorClassName,
    index,
  ]);

  let content = defaultContent;
  if (
    (meta?.name === "start" || meta?.name === "end") &&
    typeof value === "number"
  ) {
    // Start/end are stored as seconds since epoch
    content = <DateTooltipContent date={new Date(value * 1000)} withSeconds />;
  } else if (
    (meta?.isTimestamp && value != null) ||
    (typeof value === "string" && isValidTimestamp(value))
  ) {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    content = <DateTooltipContent date={new Date(value as string)} />;
  }
  if (isLoading) {
    content = null;
  }

  return (
    <TooltipContent
      className={cn(
        "max-w-[600px] p-2 overflow-hidden whitespace-pre-wrap text-xs font-normal text-primary-800",
        streamingContent ? "" : colorClassName,
        "bg-primary-50 border-primary-200",
      )}
      align="start"
      hideWhenDetached
    >
      {streamingContent || content}
    </TooltipContent>
  );
};

const genericMemo: <T>(component: T) => T = React.memo;
export default genericMemo(CellTooltipContent);
export type CellTooltipContentType = typeof CellTooltipContent;
