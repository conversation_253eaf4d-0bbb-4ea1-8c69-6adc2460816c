import type { NextApiRequest, NextApiResponse } from "next";
import { HTTPError } from "#/utils/server-util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import {
  type AuthLookup,
  getAuthorizationToken,
  loginToAuthId,
} from "../_login_to_auth_id";
import { isEmpty } from "#/utils/object";
import { getModelEndpointTypes } from "@braintrust/proxy/schema";
import { httpHandleError } from "../_request_util";
import { SqlQueryParams } from "#/utils/sql-query-params";
import Mustache from "mustache";
import { z } from "zod";

const REALTIME_VOICE_MODELS = ["gpt-4o-realtime-preview-2024-10-01"];

// RBAC_DISCLAIMER: reading org secrets is permitted to all users within the org.
async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
  authLookup: AuthLookup,
) {
  if (req.method !== "POST") {
    throw new HTTPError(405, "Only POST is supported");
  }

  let name = null;
  let types: null | string[] = null;
  let model: null | string = null;
  let orgName = null;
  let mode = null;
  try {
    name = req.body["name"];
    types = req.body["types"];
    model = req.body["model"];
    orgName = req.body["org_name"];
    mode = req.body["mode"];
  } catch {
    throw new HTTPError(400, "Malformed request");
  }

  if (model === null) {
    // Default types
    types = ["openai", "azure"];
  } else if (REALTIME_VOICE_MODELS.includes(model)) {
    // These models are not formally supported yet, so just look for any OpenAI key.
    types = ["openai"];
    model = null;
  }

  if (
    isEmpty(name) &&
    (isEmpty(types) || types.length === 0) &&
    isEmpty(model)
  ) {
    throw new HTTPError(400, "Must specify either name or types/model");
  }

  const supabase = getServiceRoleSupabase();
  try {
    const queryParams = new SqlQueryParams();
    const authIdParam = queryParams.add(authLookup.auth_id);
    const orgNameParam = queryParams.add(orgName);
    const filters = ["TRUE"];
    if (!isEmpty(name)) {
      filters.push(`decrypted_org_secrets.name = ${queryParams.add(name)}`);
    }
    if (!isEmpty(authLookup.org_id)) {
      filters.push(`organizations.id = ${queryParams.add(authLookup.org_id)}`);
    }
    if (types && types.length > 0) {
      filters.push(
        `decrypted_org_secrets.type IN (${types
          .map((t) => queryParams.add(t))
          .join(", ")})`,
      );
    }
    if (!isEmpty(model)) {
      const modelTypes = getModelEndpointTypes(model);

      if (
        modelTypes.length === 0 &&
        process.env.BRAINTRUST_ENABLE_TEST_MODELS === "true" &&
        model === "ft:gpt-3.5-turbo-0125:braintrust-data::9Rac3rrg"
      ) {
        modelTypes.push("openai");
      }

      filters.push(
        `
        (
          ${
            modelTypes.length > 0
              ? `(
                decrypted_org_secrets.type IN (${modelTypes
                  .map((t) => queryParams.add(t))
                  .join(", ")})
                AND NOT coalesce((decrypted_org_secrets.metadata::jsonb->'excludeDefaultModels')::boolean, false)
              )`
              : "FALSE"
          }
          OR
          (
            decrypted_org_secrets.metadata::jsonb->'customModels' ? (${queryParams.add(model)})
          )
        )
          `,
      );
    }

    const { rows } = await supabase.query(
      `
    SELECT
        users.email AS user_email,
        organizations.name AS org_name,
        decrypted_org_secrets.id,
        decrypted_org_secrets.type,
        decrypted_org_secrets.name,
        ${
          mode && mode.includes("skip-sec")
            ? ""
            : "decrypted_org_secrets.decrypted_secret AS secret,"
        }
        decrypted_org_secrets.metadata AS metadata
      FROM secrets.decrypted_org_secrets AS decrypted_org_secrets
      JOIN organizations ON decrypted_org_secrets.org_id = organizations.id
      JOIN members ON organizations.id = members.org_id
      JOIN users ON members.user_id = users.id
      WHERE
        users.auth_id = ${authIdParam}
        AND (organizations.name = ${orgNameParam} OR ${orgNameParam} IS NULL)
        AND ${filters.join(" AND ")}
      `,
      queryParams.params,
    );

    const processedRows = rows.map((row) => {
      const rowParsed = z
        .object({
          user_email: z.string(),
          org_name: z.string(),
          id: z.string().nullish(),
          type: z.string().nullish(),
          name: z.string().nullish(),
          secret: z.string().nullish(),
          metadata: z
            .object({
              additionalHeaders: z.record(z.string(), z.string()).optional(),
            })
            .passthrough()
            .nullish(),
        })
        .parse(row);
      const metadata = rowParsed.metadata;
      return {
        ...rowParsed,
        metadata: metadata
          ? {
              ...metadata,
              additionalHeaders: metadata.additionalHeaders
                ? Object.fromEntries(
                    Object.entries(metadata.additionalHeaders).map(
                      ([key, value]) => [
                        key,
                        Mustache.render(value, {
                          email: row.user_email,
                          ...(model ? { model } : {}),
                        }),
                      ],
                    ),
                  )
                : undefined,
            }
          : undefined,
      };
    });

    if (mode.includes("full")) {
      res.status(200).json(processedRows);
    } else {
      res.status(200).json({
        secret: processedRows[0]?.secret ?? null,
      });
    }
    res.end();
  } catch (e) {
    throw new HTTPError(400, `${e}`);
  }
}

// TODO(manu): move this to use runJsonRequest.
export default async function action(
  req: NextApiRequest,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  res: NextApiResponse<any>,
) {
  let authLookup: AuthLookup | undefined = undefined;
  try {
    authLookup = await getAPIAuthId(req, res);
    await handler(req, res, authLookup);
  } catch (error) {
    await httpHandleError({ error, authLookup, req, res });
  }
  res.end();
}

async function getAPIAuthId(req: NextApiRequest, _res: NextApiResponse) {
  const token = getAuthorizationToken((x) => req.headers[x]);
  if (token === undefined) {
    throw new HTTPError(401, "No authentication token");
  }

  if (!req.body) {
    throw new HTTPError(400, "Malformed request");
  }

  return await loginToAuthId({ token });
}
