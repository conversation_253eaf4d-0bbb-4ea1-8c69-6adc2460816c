# Braintrust

> The comprehensive AI evaluation and testing platform. Braintrust helps developers evaluate, iterate, and ship reliable AI applications with powerful logging, evaluation, and debugging tools.

## Platform

- [Braintrust Platform](https://www.braintrust.dev/): Complete AI evaluation and testing platform for modern AI development
- [AI Proxy](https://www.braintrust.dev/docs/guides/proxy): Unified API to access leading AI models from OpenAI, Anthropic, Meta, Mistral, and more
- [Playground](https://www.braintrust.dev/docs/guides/playground): Interactive environment for testing and iterating on AI prompts and models
- [Self-Hosting](https://www.braintrust.dev/docs/guides/self-hosting): Deploy Braintrust in your own infrastructure with full control and security

## Features

- [Experiments](https://www.braintrust.dev/docs/guides/experiments): Run systematic evaluations and compare AI model performance across different configurations
- [Datasets](https://www.braintrust.dev/docs/guides/datasets): Manage and version your evaluation datasets with powerful querying and filtering
- [Functions](https://www.braintrust.dev/docs/guides/functions): Create and manage prompts, scorers, and tools as reusable, versionable functions
- [Logging](https://www.braintrust.dev/docs/guides/logs): Comprehensive logging and observability for AI applications with traces and spans
- [Human Review](https://www.braintrust.dev/docs/guides/human-review): Collect human feedback and annotations to improve your AI systems

## SDKs & Libraries

- [JavaScript/TypeScript SDK](https://www.braintrust.dev/docs/reference/libs/nodejs): Full-featured SDK for logging, evaluation, and experimentation
- [Python SDK](https://www.braintrust.dev/docs/reference/libs/python): Comprehensive Python library for AI evaluation and logging
- [Autoevals](https://www.braintrust.dev/docs/reference/autoevals): Automatic evaluation library with LLM-as-judge, statistical, and heuristic methods

## Getting Started

- [Quick Start](https://www.braintrust.dev/docs/start): Get started with your first AI evaluation in minutes
- [Evaluation Guide](https://www.braintrust.dev/docs/start/eval-sdk): Learn how to use the evaluation SDK
- [UI Guide](https://www.braintrust.dev/docs/start/eval-ui): Explore the web interface for managing evaluations

## Documentation

- [Documentation Home](https://www.braintrust.dev/docs): Complete documentation portal with guides, references, and tutorials
- [Guides](https://www.braintrust.dev/docs/guides): Comprehensive guides covering all platform features
- [API Reference](https://www.braintrust.dev/docs/reference/api): Complete REST API documentation
- [Best Practices](https://www.braintrust.dev/docs/best-practices): Expert guidance for AI evaluation and testing
- [Cookbook](https://www.braintrust.dev/docs/cookbook): Practical recipes and examples for common AI evaluation tasks

## Open Source

- [Braintrust SDK](https://github.com/braintrustdata/braintrust): Open source evaluation and logging SDKs
- [AI Proxy](https://github.com/braintrustdata/braintrust/tree/main/proxy): Open source unified AI proxy
- [Autoevals](https://github.com/braintrustdata/braintrust/tree/main/autoevals): Open source automatic evaluation library

## Blog & Resources

- [Blog Home](https://www.braintrust.dev/blog): Latest updates, tutorials, and insights on AI evaluation
- [Best Practices for Evals](https://www.braintrust.dev/blog/best-practices): Essential practices for AI evaluation
- [Evaluating Agents](https://www.braintrust.dev/blog/evaluating-agents): Strategies for evaluating AI agents
- [AI Development Loops](https://www.braintrust.dev/blog/ai-development-loops): The development cycle for AI applications

## Community

- [GitHub](https://github.com/braintrustdata/braintrust): Source code, issues, and contributions
- [Discord](https://discord.gg/braintrust): Join our community for support and discussions
- [Twitter](https://twitter.com/braintrustdata): Follow us for announcements and tips
