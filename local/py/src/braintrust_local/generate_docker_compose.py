import sys
import textwrap

from .test_proxy_util import get_test_proxy_server_config


def format_str(s, strip_surrounding_newlines=True):
    if strip_surrounding_newlines:
        s = s.strip("\n")
    s = s.replace(OMIT_STR + "\n", "")
    if s.endswith("\n" + OMIT_STR):
        s = s[: -(1 + len(OMIT_STR))]
    return s


OMIT_STR = "___OMIT___"


# This is the prod license key for the braintrustdata.com org. It should not be
# a big deal if this key exists in our private repo.
PROD_BRAINSTORE_LICENSE_KEY = "brainstore-eyJsaWNlbnNlS2V5VmVyc2lvbiI6MSwib3JnSWQiOiI1ZDdjOTdkNy1mZWYxLTRjYjctYmRhNi03ZTM3NTZhMGNhOGUiLCJjcmVhdGVkIjoiMjAyNS0wMS0yMlQyMzoyNToyMy43MjBaIiwic2lnbmF0dXJlIjoiam9IZTNYUFVIMHJScFI0WWhsQ1NuZVlxcmdpVUZZMmZPOGl2RGgzQndkSklLblFoZ0htNndxbVdhTkY2c29PSFVpWjFaMXZjQ0ZKSnA4WDZLRFBGQXc9PSJ9"
PLACEHOLDER_BRAINSTORE_LICENSE_KEY = "${BRAINSTORE_LICENSE_KEY}"
TEST_PROXY_CONFIG = get_test_proxy_server_config()

REALTIME_URL = "http://host.docker.internal:8788"
# Here we are using the ws scheme because the webapp connects to the realtime
# service over websockets.
REALTIME_PUBLIC_URL = "ws://localhost:8788"
PG_URL = "postgres://postgres:<EMAIL>:5532/postgres"
REDIS_URL = "redis://host.docker.internal:6479/0"
BRAINTRUST_APP_URL = "http://host.docker.internal:3000"
BRAINTRUST_APP_PUBLIC_URL = "http://localhost:3000"
TS_API_ASYNC_SCORING_PROXY_URL = f"http://host.docker.internal:{TEST_PROXY_CONFIG.port}"
DEFAULT_FUNCTION_SECRET_KEY = "foobar"
BRAINSTORE_PORT = 4000
BRAINSTORE_URL = "http://braintrust-brainstore:4000"
BRAINSTORE_S3_BUCKET = dict(test="code-bundles", api="<YOUR_S3_BUCKET_NAME>")
BRAINSTORE_S3_BUCKET_PREFIX = dict(test="brainstore/", api="")
BRAINSTORE_LICENSE_KEY = dict(test=PROD_BRAINSTORE_LICENSE_KEY, api=PLACEHOLDER_BRAINSTORE_LICENSE_KEY)
MINIO_URL = "http://host.docker.internal:10000"


def generate(mode):
    assert mode in ["api", "test"]

    HEADER_ADDENDUM = format_str(
        """
#
# Many of the environment variables have a default version as well as a "public"
# version (e.g. `BRAINTRUST_APP_URL` and `BRAINTRUST_APP_PUBLIC_URL`). The
# reason is that docker containers sometimes need different URLs than external
# clients to communicate with other containers on the same machine. For
# instance, docker containers must use the domain `host.docker.internal` instead
# of `localhost` to refer to other services on the same host network.
#
# Some environment variables are used for multiple containers. Usually they will
# be the same, but depending on exactly how you deploy the services, you may
# need to set different values for the same env variable on different
# containers. We document the set of available environment variables here.
"""
    )
    header_addendum_mode_map = dict(test=HEADER_ADDENDUM, api=OMIT_STR)

    STANDALONE_API_TEST_ENV = format_str(
        f"""
      # Point the API to connect to the locally-hosted components.
      BRAINTRUST_APP_URL: {BRAINTRUST_APP_URL}
      BRAINTRUST_APP_PUBLIC_URL: {BRAINTRUST_APP_PUBLIC_URL}
      # Here we are using the http scheme because the backend API connects to
      # the realtime service over HTTP.
      REALTIME_URL: {REALTIME_URL}

      # NOTE: keep general modifications to this section in sync with
      # api-ts/.env.development. Also specifically keep
      # ASYNC_SCORING_FETCH_PAYLOAD_THRESHOLD with
      # tests/bt_services/test_async_scoring.py
      TS_API_HEALTHSERVER_HOST: 0.0.0.0
      TS_API_HEALTHSERVER_PORT: 8790
      CHALICE_LOCAL_USE_LOCAL_ENV: 1
      BRAINTRUST_HOSTED_DATA_PLANE: true
      TS_API_ASYNC_SCORING_PROXY_URL: {TS_API_ASYNC_SCORING_PROXY_URL}
      FUNCTION_SECRET_KEY: {DEFAULT_FUNCTION_SECRET_KEY}
      TELEMETRY_ENABLED: true
      TELEMETRY_URL: http://host.docker.internal:8001/events
      TELEMETRY_TOKEN: foobar
      RESPONSE_BUCKET_S3_ENDPOINT: {MINIO_URL}
      ASYNC_SCORING_FETCH_PAYLOAD: true
      ASYNC_SCORING_FETCH_PAYLOAD_THRESHOLD: 65536
      BRAINSTORE_INSERT_ROW_REFS: true
      INSERT_LOGS2: true
      BRAINSTORE_PROJECT_LOGS_REALTIME_READ_TIMEOUT_MS: 0
      TESTING_ONLY_ALLOW_QUERY_FULL_AUDIT_LOG: true
      TESTING_ONLY_ALLOW_SPOOF_SELF_HOSTED_DATA_PLANE: true
      TESTING_ONLY_ALLOW_ORG_NAME_HEADER_OVERRIDE: true

      RATELIMIT_API_LOGS_ORG: c5292878-410f-4bbb-bffc-c61f9e624805=5
      RATELIMIT_API_LOGS_ORG_WINDOW_SECS: 10
      RATELIMIT_API_LOGS_ORG_ENFORCE: true
"""
    )
    standalone_api_test_mode_map = dict(test=STANDALONE_API_TEST_ENV, api=OMIT_STR)

    STANDALONE_API_ADDITIONAL_SERVICES_ADDENDUM = format_str(
        f"""
      # If you are deploying any other services yourself, such as
      # realtime, you may override their URLs here as well.
      # REALTIME_URL: {REALTIME_URL}
"""
    )
    standalone_api_additional_services_addendum_mode_map = dict(
        test=OMIT_STR, api=STANDALONE_API_ADDITIONAL_SERVICES_ADDENDUM
    )

    STANDALONE_API_TEST_PORTS = format_str(
        """
      - 8790:8790
"""
    )
    standalone_api_test_ports_mode_map = dict(test=STANDALONE_API_TEST_PORTS, api=OMIT_STR)

    BRAINSTORE_TEST_ENVIRONMENT_VARIABLES = format_str(
        f"""
      # Suppress verbose info messages.
      BRAINSTORE_SUPPRESS_VERBOSE_INFO: true

      # Ignore swap space when calculating system memory.  We do this because we
      # don't know what host system the docker container may be run on, so
      # simply forking the implementation on target_os is not enough.
      BRAINSTORE_SYSTEM_MEMORY_IGNORE_SWAP: true

      AWS_ENDPOINT_URL: {MINIO_URL}
      AWS_ACCESS_KEY_ID: minio_root_user
      AWS_SECRET_ACCESS_KEY: minio_root_password
      AWS_ALLOW_HTTP: true

      # No need to stress out dev machines with a large cache.
      BRAINSTORE_OBJECT_STORE_CACHE_MEMORY_LIMIT: 1MB
      BRAINSTORE_OBJECT_STORE_CACHE_FILE_SIZE: 1GB
      BRAINSTORE_INDEX_WRITER_VALIDATE_AFTER_COMPLETION: true
      BRAINSTORE_LOCKS_MANAGER_ENABLE_BOOKKEEPING: true

      # Don't check the memory limit for realtime WAL entries, so that unit
      # tests don't randomly hit the limit.
      BRAINSTORE_SKIP_REALTIME_WAL_MEMORY_LIMIT: true
      BRAINSTORE_REALTIME_READ_TIMEOUT_MS: 0

      BRAINSTORE_VACUUM_INDEX_DELETION_GRACE_PERIOD_SECONDS: 2
      BRAINSTORE_VACUUM_INDEX_LAST_WRITTEN_SLOP_SECONDS: 1
      BRAINSTORE_VACUUM_INDEX_PERIOD_SECONDS: 2
      BRAINSTORE_BACKGROUND_VACUUM_SLEEP_SECONDS: 1

      SERVICE_TOKEN_SECRET_KEY: foobar
      BRAINTRUST_APP_URL: {BRAINTRUST_APP_URL}
"""
    )
    brainstore_test_environment_variables_mode_map = dict(test=BRAINSTORE_TEST_ENVIRONMENT_VARIABLES, api=OMIT_STR)

    BRAINSTORE = format_str(
        f"""
  braintrust-brainstore:
    image: public.ecr.aws/braintrust/brainstore:latest
    environment:
      BRAINSTORE_VERBOSE: 1
      BRAINSTORE_PORT: {BRAINSTORE_PORT}
      BRAINSTORE_METADATA_URI: {PG_URL}
      BRAINSTORE_WAL_URI: {PG_URL}
      BRAINSTORE_LOCKS_URI: s3://{BRAINSTORE_S3_BUCKET[mode]}/{BRAINSTORE_S3_BUCKET_PREFIX[mode]}locks
      BRAINSTORE_INDEX_URI: s3://{BRAINSTORE_S3_BUCKET[mode]}/{BRAINSTORE_S3_BUCKET_PREFIX[mode]}index
      BRAINSTORE_REALTIME_WAL_URI: s3://{BRAINSTORE_S3_BUCKET[mode]}/{BRAINSTORE_S3_BUCKET_PREFIX[mode]}wal
      # You must set this environment variable in your local environment for
      # the Brainstore container to start.
      BRAINSTORE_LICENSE_KEY: {BRAINSTORE_LICENSE_KEY[mode]}
      NO_COLOR: 1
{brainstore_test_environment_variables_mode_map[mode]}
    ports:
      - 4000:4000
    extra_hosts:
      - "host.docker.internal:host-gateway"
"""
    )

    BRAINSTORE_COMMENTED = format_str(
        f"""
  # Uncomment this to enable Brainstore.
{textwrap.indent(BRAINSTORE, "#")}
"""
    )

    brainstore_mode_map = dict(
        test=BRAINSTORE,
        api=BRAINSTORE_COMMENTED,
    )

    S3_MINIO_SETUP_DEPENDENCY = format_str(
        """
    depends_on:
        setup-s3-local:
          condition: service_completed_successfully
"""
    )
    s3_minio_setup_dependency_mode_map = dict(test=S3_MINIO_SETUP_DEPENDENCY, api=OMIT_STR)

    SETUP_S3_MINIO_BUCKETS = format_str(
        f"""
  setup-s3-local:
    image: amazon/aws-cli:latest
    entrypoint: /bin/sh -c
    command: >
      "if ! aws s3 ls | grep {BRAINSTORE_S3_BUCKET[mode]}; then
        aws s3 mb s3://{BRAINSTORE_S3_BUCKET[mode]};
      fi"
    environment:
      - AWS_ENDPOINT_URL={MINIO_URL}
      - AWS_ACCESS_KEY_ID=minio_root_user
      - AWS_SECRET_ACCESS_KEY=minio_root_password
      - AWS_ALLOW_HTTP=true
    extra_hosts:
      - "host.docker.internal:host-gateway"
"""
    )
    setup_s3_local_mode_map = dict(test=SETUP_S3_MINIO_BUCKETS, api=OMIT_STR)

    STANDALONE_REALTIME = format_str(
        f"""
  braintrust-standalone-realtime:
    image: public.ecr.aws/braintrust/standalone-realtime:latest
    ports:
      - 8788:8788
    extra_hosts:
      - "host.docker.internal:host-gateway"
"""
    )

    STANDALONE_REALTIME_COMMENTED = format_str(
        f"""
  # To enable the realtime service, un-comment the service definition below. The realtime service
  # is optional.
{textwrap.indent(STANDALONE_REALTIME, "#")}
"""
    )

    standalone_realtime_mode_map = dict(
        test=STANDALONE_REALTIME,
        api=STANDALONE_REALTIME_COMMENTED,
    )

    STANDALONE_API_BRAINSTORE_ENVIRONMENT_VARIABLES_DISABLED = format_str(
        f"""
      # Uncomment this to enable Brainstore.
      # BRAINSTORE_ENABLED: true
      # BRAINSTORE_URL: {BRAINSTORE_URL}
"""
    )
    STANDALONE_API_BRAINSTORE_ENVIRONMENT_VARIABLES_ENABLED = format_str(
        f"""
      BRAINSTORE_ENABLED: true
      BRAINSTORE_URL: {BRAINSTORE_URL}
"""
    )
    standalone_api_brainstore_environment_variables_mode_map = dict(
        test=STANDALONE_API_BRAINSTORE_ENVIRONMENT_VARIABLES_ENABLED,
        api=STANDALONE_API_BRAINSTORE_ENVIRONMENT_VARIABLES_DISABLED,
    )

    STANDALONE_API_BRAINSTORE_TEST_ENVIRONMENT_VARIABLES = format_str(
        f"""
      BRAINSTORE_REALTIME_WAL_BUCKET: {BRAINSTORE_S3_BUCKET[mode]}
      BRAINSTORE_REALTIME_WAL_BUCKET_PREFIX: {BRAINSTORE_S3_BUCKET_PREFIX[mode]}wal
      BRAINSTORE_DEFAULT: force
"""
    )
    standalone_api_brainstore_test_environment_variables_mode_map = dict(
        test=STANDALONE_API_BRAINSTORE_TEST_ENVIRONMENT_VARIABLES,
        api=OMIT_STR,
    )

    STANDALONE_API_DEPENDS_ON_BRAINSTORE = format_str(
        """
      braintrust-brainstore:
        condition: service_healthy
"""
    )

    STANDALONE_API_DEPENDS_ON_BRAINSTORE_COMMENTED = format_str(
        f"""
      # Uncomment this to enable Brainstore.
{textwrap.indent(STANDALONE_API_DEPENDS_ON_BRAINSTORE, "#")}
"""
    )

    standalone_api_depends_on_brainstore_mode_map = dict(
        test=STANDALONE_API_DEPENDS_ON_BRAINSTORE,
        api=STANDALONE_API_DEPENDS_ON_BRAINSTORE_COMMENTED,
    )

    ret = format_str(
        f"""
# Each docker container can be configured with environment variables. Many of
# these variables point the container to the other services, which may be other
# containers you have deployed or centrally-hosted Braintrust services.
#
# Although we include containers here for Redis and Postgres, you should use
# cloud-native offerings (like RDS, Google Cloud SQL, Azure Managed Postgres, etc.)
# for production environments.
{header_addendum_mode_map[mode]}
#
# - REDIS_URL: Connection URI for the redis instance for internal container use.
# The general form of the URI is
# `redis://[user[:password]@][host][:port][/db-number][?param1=value1&param2=value2...]`.
# Defaults to the URI for the `braintrust-redis` docker service.
#
# - PG_URL: Connection URI for the postgres instance for internal container use.
# The general form of the URI is
# `postgresql://[user[:password]@][host][:port][/dbname][?param1=value1&param2=value2...]`.
# Defaults to the URI for the 'braintrust-postgres' docker service. See
# https://www.postgresql.org/docs/current/libpq-connect.html#LIBPQ-CONNSTRING-URIS
# for full details on the URI spec.
#
# - PG_POOL_CONFIG_MAX_NUM_CLIENTS: Configure the maximum number of postgres
# clients allowed in use by the API server. Defaults to 10.
#
# - RESPONSE_BUCKET_NAME: The S3 (or protocol-compatible) bucket to use for storing API responses
# that exceed a certain threshold (RESPONSE_BUCKET_OVERFLOW_THRESHOLD).
#
# - RESPONSE_BUCKET_OVERFLOW_THRESHOLD: The threshold (in bytes) that an API response must exceed
# in order to be stored in the RESPONSE_BUCKET. This is useful for serverless environments where the
# responses have a maximum payload size.
#
# - RESPONSE_BUCKET_PREFIX: The prefix to use when storing responses in the RESPONSE_BUCKET.
#
# - RESPONSE_BUCKET_ACCESS_KEY_ID: Together with RESPONSE_BUCKET_SECRET_ACCESS_KEY, the credentials
# to use when uploading to the RESPONSE_BUCKET. If unspecified, uses the AWS credentials from the
# container's environment.
#
# - RESPONSE_BUCKET_SECRET_ACCESS_KEY: Together with RESPONSE_BUCKET_ACCESS_KEY_ID, the credentials
# to use when uploading to the RESPONSE_BUCKET. If unspecified, uses the AWS credentials from the
# container's environment.
#
# - RESPONSE_BUCKET_S3_ENDPOINT: The S3 (or protocol-compatible) endpoint to use when uploading to
# the RESPONSE_BUCKET (defaults to AWS at RESPONSE_BUCKET_REGION).
#
# - RESPONSE_BUCKET_REGION: The region of the RESPONSE_BUCKET.
#
# - ALLOW_CODE_FUNCTION_EXECUTION: Whether to allow custom code (TypeScript and Python)
# to execute. This is safe to enable as long as you are running code that you trust to not
# be malicious (i.e. do not expose this to the public). If you would like to support running
# untrusted code, then contact <NAME_EMAIL> so we can ensure you've configured
# the appropriate security controls.
#
# - CODE_BUNDLE_BUCKET: The S3 (or protocol-compatible) bucket to use for storing code bundles. Uses
# the same region and credentials as the RESPONSE_BUCKET. By default, the code bundle bucket is also
# used to store attachments under the `attachments/` prefix.
#
# - ATTACHMENT_BUCKET: The S3 (or protocol-compatible) bucket to use for storing attachments. Uses
# the same region and credentials as the RESPONSE_BUCKET. This overrides the default behavior of
# storing attachments in the code bundle bucket.
#
# - FUNCTION_SECRET_KEY: The key to use for encrypting function env secrets. Function environment
# secrets can be specified per org, project, or function and are exposed to functions as environment
# variables. This key is used to symmetrically encrypt (via AES-GCM 256) function secrets before storing
# them in the database. Once this key is set, if you change it, you will no longer be able to access
# any saved function secrets.
#
# - ORG_NAME: A specific org name to use for this deployment or * to allow any org to access it. If unspecified,
# `ORG_NAME` defaults to `*`. If specified, then certain operations, like AI secrets, will be
# constrained to only the specified org.
#

# Brainstore
#
# Brainstore is an optional service that enables very fast real-time search.
# To use Brainstore, you must have a dedicated S3 bucket for Brainstore data, as well as
# a license key provided by Braintrust. Brainstore will need to access the Postgres database
# and Redis instance.
#
# To enable Brainstore, uncomment the service definition below, as well as the environment variables
# and dependency in the standalone-api service definition.
# - BRAINSTORE_METADATA_URI:
#     This should point to the existing Braintrust Postgres database.
# - BRAINSTORE_WAL_URI:
#     This should point to the existing Braintrust Postgres database.
# - BRAINSTORE_LOCKS_URI:
#     You should change <YOUR_S3_BUCKET_NAME> to the name of your Brainstore S3 bucket. Leave the path as is.
# - BRAINSTORE_INDEX_URI:
#     You should change <YOUR_S3_BUCKET_NAME> to the name of your Brainstore S3 bucket. Leave the path as is.
# - BRAINSTORE_REALTIME_WAL_URI:
#     You should change <YOUR_S3_BUCKET_NAME> to the name of your Brainstore S3 bucket. Leave the path as is.
# - BRAINSTORE_LICENSE_KEY:
#     This should be the license key provided by Braintrust. Brainstore will not start without this.
#

services:
  braintrust-redis:
    image: public.ecr.aws/braintrust/redis:latest
    ports:
      - 6479:6379
    extra_hosts:
      - "host.docker.internal:host-gateway"
  braintrust-postgres:
    image: public.ecr.aws/braintrust/postgres:latest
    command: postgres -c config_file=/etc/postgresql.conf
    environment:
      POSTGRES_DB: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_USER: postgres
    ports:
      - 5532:5432
    extra_hosts:
      - "host.docker.internal:host-gateway"
    volumes:
      - api_pg_volume:/var/lib/postgresql/data
{brainstore_mode_map[mode]}
{s3_minio_setup_dependency_mode_map[mode]}
{setup_s3_local_mode_map[mode]}
  braintrust-standalone-api:
    image: public.ecr.aws/braintrust/standalone-api:latest
    environment:
      PG_URL: {PG_URL}
      REDIS_URL: {REDIS_URL}
      ALLOW_CODE_FUNCTION_EXECUTION: true
      # Set this to 80% for production deployments that use a dedicated node for the API
      # NODE_MEMORY_PERCENT: 80
      # Any requests made to the container from within itself will use the
      # loopback URL rather than whatever externally-visible URL is set in the
      # org configuration.
      BRAINTRUST_API_URL: http://127.0.0.1:8000
{standalone_api_test_mode_map[mode]}
{standalone_api_additional_services_addendum_mode_map[mode]}
{standalone_api_brainstore_environment_variables_mode_map[mode]}
{standalone_api_brainstore_test_environment_variables_mode_map[mode]}
    ports:
      - 8000:8000
{standalone_api_test_ports_mode_map[mode]}
    extra_hosts:
      - "host.docker.internal:host-gateway"
    depends_on:
      braintrust-redis:
        condition: service_healthy
      braintrust-postgres:
        condition: service_healthy
{standalone_api_depends_on_brainstore_mode_map[mode]}
{standalone_realtime_mode_map[mode]}
volumes:
  api_pg_volume: null
"""
    )
    return ret


def main():
    print(generate(sys.argv[1]))
