[package]
name = "btql"
version = "0.1.0"
edition = "2021"

[dependencies]
tantivy = { path = "../tantivy", optional = true }
regex-automata = "0.4.9"
util = { path = "../util" }

lazy_static = "1.5.0"
serde = { version = "1.0.210", features = ["derive"] }
serde_json = "1.0.128"
time = { version = "0.3.37", features = ["formatting", "macros"] }
log = "0.4.22"
base64 = "0.22.1"
thiserror = "1.0"
regex = "1.11.0"
tantivy_common = { package = "tantivy-common", git = "https://github.com/braintrustdata/tantivy.git", rev = "8b6f84f891caae8f07a9353e344ebf65f071e118" }


[features]
default = ["tantivy"]
tantivy = ["dep:tantivy"]
wasm = ["time/wasm-bindgen"]
