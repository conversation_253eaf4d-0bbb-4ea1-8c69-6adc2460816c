Unexpected Http Driver Exception
Cannot connect to Clickhouse. This is likely ok!
Error HTTPConnectionPool(host='localhost', port=8123): Max retries exceeded with url: /?wait_end_of_query=1 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f0edcbe2a50>: Failed to establish a new connection: [Errno 111] Connection refused')) executing HTTP request attempt 1 http://localhost:8123
Running pg migrations
No pending migrations found
Running clickhouse migrations
{"level":30,"time":1755172615781,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","gitCommit":"23f4f06557fae20cfa21f97164e90a74a7000550","msg":"COMMIT"}
(node:8) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
{"level":30,"time":1755172615781,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","deploymentMode":"local","msg":"DEPLOYMENT_MODE"}
{"level":30,"time":1755172615781,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","msg":"Starting Brainstore ETL loop"}
{"level":30,"time":1755172615781,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","msg":"Starting Automation Cron loop"}
{"level":30,"time":1755172615792,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","msg":"[server]: Server is running at http://0.0.0.0:8000"}
{"level":40,"time":1755172618403,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","pair":"","traceId":"f227667f25ef53cdce8c68329ebc00eb","spanId":"fcb01f5831003ea7","spanName":"POST","msg":"Invalid org rate limit value"}
{"level":40,"time":1755172618403,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","windowStr":"","traceId":"f227667f25ef53cdce8c68329ebc00eb","spanId":"fcb01f5831003ea7","spanName":"POST","msg":"Invalid window value disables org rate limiting"}
{"level":40,"time":1755172748326,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","mergeIds":"{\"objectType\":\"experiment\",\"objectId\":\"297b7c74-8db4-4cbf-b663-3e2db55decc8\",\"id\":\"4f115c01-15e6-4a09-82fb-4bacef06e80d\"}, {\"objectType\":\"experiment\",\"objectId\":\"297b7c74-8db4-4cbf-b663-3e2db55decc8\",\"id\":\"2b54ec79-2b93-4241-8f7e-34dc9115673b\"}, {\"objectType\":\"experiment\",\"objectId\":\"297b7c74-8db4-4cbf-b663-3e2db55decc8\",\"id\":\"0dc081f0-3347-4f32-90e7-1e5ba0cfd12b\"}","traceId":"65cc010f4a4edb45d26777fba1720e72","spanId":"04f6eb68856a6abb","spanName":"write rows to pg","msg":"Merge advisory locks timeout"}
{"level":50,"time":1755172748326,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","error":"Timed out waiting for merge advisory locks","orgIds":["6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78"],"traceId":"65cc010f4a4edb45d26777fba1720e72","spanId":"6f6099498d4e33ed","spanName":"POST","msg":"Query timed out"}
{"level":40,"time":1755172748326,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"65cc010f4a4edb45d26777fba1720e72","spanId":"6f6099498d4e33ed","spanName":"POST","msg":"Forcibly terminating process at pid 379780"}
{"level":40,"time":1755172748472,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"65cc010f4a4edb45d26777fba1720e72","spanId":"4b9daf08b71300b4","attributes":{"error.type":"Xu","error.status_code":200,"error_context.userId":"e42f8940-05fb-4899-87cd-ba958f0d9b22","error_context.userEmail":"<EMAIL>","error_context.orgName":"Microsoft Copilot PME"},"events":[{"name":"exception","attributes":{"exception.type":"Error","exception.message":"Too many concurrent insert requests","exception.stacktrace":"Error: Too many concurrent insert requests\n    at eDm (/braintrust/api-ts/local/local.js:3526:994)\n    at async j2m (/braintrust/api-ts/local/local.js:3364:8609)\n    at async lst (/braintrust/api-ts/local/local.js:3614:6279)\n    at async Q4e (/braintrust/api-ts/local/local.js:3614:6440)"},"time":[1755172748,472219023],"droppedAttributesCount":0}],"startTime":"2025-08-14T11:59:08.472Z","durationMs":"0.22","spanName":"error","msg":"error"}
{"level":30,"time":1755172748474,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","method":"POST","path":"/logs3","statusCode":429,"error":"\nXu [Error]: Too many concurrent insert requests\n    at eDm (/braintrust/api-ts/local/local.js:3526:994)\n    at async j2m (/braintrust/api-ts/local/local.js:3364:8609)\n    at async lst (/braintrust/api-ts/local/local.js:3614:6279)\n    at async Q4e (/braintrust/api-ts/local/local.js:3614:6440) {\n  status: 429\n}","errorContext":{"userId":"e42f8940-05fb-4899-87cd-ba958f0d9b22","userEmail":"<EMAIL>","orgName":"Microsoft Copilot PME"},"traceId":"65cc010f4a4edb45d26777fba1720e72","spanId":"6f6099498d4e33ed","spanName":"POST","msg":"REQUEST WILL FAIL: Too many concurrent insert requests"}
{"level":50,"time":1755173233737,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","callerIdentifier":"[BrainstoreTS] [updateFrontierLoop] logs2","error":{},"traceId":"9eeba8c44fe5ce87f4701e4a4849e1e7","spanId":"6e43ea7192965156","spanName":"POST","msg":"Operation failed, continuing..."}
{"level":50,"time":1755173233737,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","callerIdentifier":"[BrainstoreTS] [updateFrontierLoop] logs","error":{},"traceId":"9eeba8c44fe5ce87f4701e4a4849e1e7","spanId":"6e43ea7192965156","spanName":"POST","msg":"Operation failed, continuing..."}
{"level":50,"time":1755173834954,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","callerIdentifier":"[BrainstoreTS] [updateFrontierLoop] logs2","error":{},"traceId":"cf9432e5bce7c5fc7be0101122c757cb","spanId":"30533fc38d7ff77f","spanName":"POST","msg":"Operation failed, continuing..."}
{"level":50,"time":1755173834955,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","callerIdentifier":"[BrainstoreTS] [updateFrontierLoop] logs","error":{},"traceId":"cf9432e5bce7c5fc7be0101122c757cb","spanId":"30533fc38d7ff77f","spanName":"POST","msg":"Operation failed, continuing..."}
{"level":50,"time":1755174436240,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","callerIdentifier":"[BrainstoreTS] [updateFrontierLoop] logs","error":{},"traceId":"0c6db689bb86b1c028d9373e6141e3e4","spanId":"1f41a16457f0dd90","spanName":"POST","msg":"Operation failed, continuing..."}
{"level":50,"time":1755174436241,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","callerIdentifier":"[BrainstoreTS] [updateFrontierLoop] logs2","error":{},"traceId":"0c6db689bb86b1c028d9373e6141e3e4","spanId":"1f41a16457f0dd90","spanName":"POST","msg":"Operation failed, continuing..."}
{"level":40,"time":1755174746637,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","mergeIds":"{\"objectType\":\"experiment\",\"objectId\":\"c790384d-e384-409d-a37c-b67f3238e3e6\",\"id\":\"86d886ba-b90e-46c2-b2c6-211211636965\"}, {\"objectType\":\"experiment\",\"objectId\":\"c790384d-e384-409d-a37c-b67f3238e3e6\",\"id\":\"5b9158c4-8c38-42bb-a93d-bd5191f2e405\"}, {\"objectType\":\"experiment\",\"objectId\":\"c790384d-e384-409d-a37c-b67f3238e3e6\",\"id\":\"2082f3e4-17d5-4654-81b3-3c0e00210b39\"}, {\"objectType\":\"experiment\",\"objectId\":\"c790384d-e384-409d-a37c-b67f3238e3e6\",\"id\":\"fcb20c21-b37b-46ea-ac84-44999c516023\"}, {\"objectType\":\"experiment\",\"objectId\":\"c790384d-e384-409d-a37c-b67f3238e3e6\",\"id\":\"4c5b768a-72f7-43ee-927f-9bc7f4e5f63a\"}, {\"objectType\":\"experiment\",\"objectId\":\"c790384d-e384-409d-a37c-b67f3238e3e6\",\"id\":\"0692d19e-76f5-4353-b756-907477b75aae\"}","traceId":"2538698d327278126c3e9cf638c642cd","spanId":"cc237ed483de2ad3","spanName":"write rows to pg","msg":"Merge advisory locks timeout"}
{"level":50,"time":1755174746637,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","error":"Timed out waiting for merge advisory locks","orgIds":["6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78"],"traceId":"2538698d327278126c3e9cf638c642cd","spanId":"fdb2501f1e232605","spanName":"POST","msg":"Query timed out"}
{"level":40,"time":1755174746638,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"2538698d327278126c3e9cf638c642cd","spanId":"fdb2501f1e232605","spanName":"POST","msg":"Forcibly terminating process at pid 379778"}
{"level":40,"time":1755174746647,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"2538698d327278126c3e9cf638c642cd","spanId":"b970b2d980969c77","attributes":{"error.type":"Xu","error.status_code":200,"error_context.userId":"32778b09-962d-4d6a-bf33-a81901b95076","error_context.userEmail":"<EMAIL>","error_context.orgName":"Microsoft Copilot PME"},"events":[{"name":"exception","attributes":{"exception.type":"Error","exception.message":"Too many concurrent insert requests","exception.stacktrace":"Error: Too many concurrent insert requests\n    at eDm (/braintrust/api-ts/local/local.js:3526:994)\n    at async j2m (/braintrust/api-ts/local/local.js:3364:8609)\n    at async lst (/braintrust/api-ts/local/local.js:3614:6279)\n    at async Q4e (/braintrust/api-ts/local/local.js:3614:6440)"},"time":[1755174746,646220939],"droppedAttributesCount":0}],"startTime":"2025-08-14T12:32:26.646Z","durationMs":"0.22","spanName":"error","msg":"error"}
{"level":30,"time":1755174746648,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","method":"POST","path":"/logs3","statusCode":429,"error":"\nXu [Error]: Too many concurrent insert requests\n    at eDm (/braintrust/api-ts/local/local.js:3526:994)\n    at async j2m (/braintrust/api-ts/local/local.js:3364:8609)\n    at async lst (/braintrust/api-ts/local/local.js:3614:6279)\n    at async Q4e (/braintrust/api-ts/local/local.js:3614:6440) {\n  status: 429\n}","errorContext":{"userId":"32778b09-962d-4d6a-bf33-a81901b95076","userEmail":"<EMAIL>","orgName":"Microsoft Copilot PME"},"traceId":"2538698d327278126c3e9cf638c642cd","spanId":"fdb2501f1e232605","spanName":"POST","msg":"REQUEST WILL FAIL: Too many concurrent insert requests"}
{"level":50,"time":1755175037605,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","callerIdentifier":"[BrainstoreTS] [updateFrontierLoop] logs","error":{},"traceId":"4d48601ed45634642a2c1566b08a8a9d","spanId":"e93c9d7eab5e7190","spanName":"POST","msg":"Operation failed, continuing..."}
{"level":50,"time":1755175037605,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","callerIdentifier":"[BrainstoreTS] [updateFrontierLoop] logs2","error":{},"traceId":"4d48601ed45634642a2c1566b08a8a9d","spanId":"e93c9d7eab5e7190","spanName":"POST","msg":"Operation failed, continuing..."}
{"level":40,"time":1755175726969,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","mergeIds":"{\"objectType\":\"experiment\",\"objectId\":\"c790384d-e384-409d-a37c-b67f3238e3e6\",\"id\":\"e01a8eba-a848-4861-8db9-8a9a5831022b\"}, {\"objectType\":\"experiment\",\"objectId\":\"c790384d-e384-409d-a37c-b67f3238e3e6\",\"id\":\"e78f1e90-95b8-434f-9fe1-77afcea64e54\"}, {\"objectType\":\"experiment\",\"objectId\":\"c790384d-e384-409d-a37c-b67f3238e3e6\",\"id\":\"7fa5608a-ac67-4167-a5d1-21053d6a3e6d\"}","traceId":"40b536a794b8e4b02fca11f7b518421c","spanId":"c42ad68872f92b5a","spanName":"write rows to pg","msg":"Merge advisory locks timeout"}
{"level":50,"time":1755175726969,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","error":"Timed out waiting for merge advisory locks","orgIds":["6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78"],"traceId":"40b536a794b8e4b02fca11f7b518421c","spanId":"9f0d732c8c2bc805","spanName":"POST","msg":"Query timed out"}
{"level":40,"time":1755175726970,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"40b536a794b8e4b02fca11f7b518421c","spanId":"9f0d732c8c2bc805","spanName":"POST","msg":"Forcibly terminating process at pid 395434"}
{"level":40,"time":1755175726975,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"40b536a794b8e4b02fca11f7b518421c","spanId":"714e53a0715232e1","attributes":{"error.type":"Xu","error.status_code":200,"error_context.userId":"32778b09-962d-4d6a-bf33-a81901b95076","error_context.userEmail":"<EMAIL>","error_context.orgName":"Microsoft Copilot PME"},"events":[{"name":"exception","attributes":{"exception.type":"Error","exception.message":"Too many concurrent insert requests","exception.stacktrace":"Error: Too many concurrent insert requests\n    at eDm (/braintrust/api-ts/local/local.js:3526:994)\n    at async j2m (/braintrust/api-ts/local/local.js:3364:8609)\n    at async lst (/braintrust/api-ts/local/local.js:3614:6279)\n    at async Q4e (/braintrust/api-ts/local/local.js:3614:6440)"},"time":[1755175726,975209702],"droppedAttributesCount":0}],"startTime":"2025-08-14T12:48:46.975Z","durationMs":"0.21","spanName":"error","msg":"error"}
{"level":30,"time":1755175726976,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","method":"POST","path":"/logs3","statusCode":429,"error":"\nXu [Error]: Too many concurrent insert requests\n    at eDm (/braintrust/api-ts/local/local.js:3526:994)\n    at async j2m (/braintrust/api-ts/local/local.js:3364:8609)\n    at async lst (/braintrust/api-ts/local/local.js:3614:6279)\n    at async Q4e (/braintrust/api-ts/local/local.js:3614:6440) {\n  status: 429\n}","errorContext":{"userId":"32778b09-962d-4d6a-bf33-a81901b95076","userEmail":"<EMAIL>","orgName":"Microsoft Copilot PME"},"traceId":"40b536a794b8e4b02fca11f7b518421c","spanId":"9f0d732c8c2bc805","spanName":"POST","msg":"REQUEST WILL FAIL: Too many concurrent insert requests"}
{"level":40,"time":1755175765061,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"b651bc2bf1c873ddcaacfb1a7f003088","spanId":"0a85f550a930bc2d","attributes":{"http.request.method":"POST","http.request.method_original":"POST","url.full":"https://realtime.braintrustapi.com/broadcast","url.path":"/broadcast","url.query":"","url.scheme":"https","server.address":"realtime.braintrustapi.com","server.port":443,"network.peer.address":"***********","network.peer.port":443},"events":[{"name":"exception","attributes":{"exception.type":"ECONNRESET","exception.message":"read ECONNRESET","exception.stacktrace":"Error: read ECONNRESET\n    at TLSWrap.onStreamRead (node:internal/stream_base_commons:216:20)\n    at TLSWrap.callbackTrampoline (node:internal/async_hooks:130:17)"},"time":[1755175765,60483867],"droppedAttributesCount":0}],"startTime":"2025-08-14T12:49:25.039Z","durationMs":"21.49","spanName":"POST","msg":"POST"}
{"level":50,"time":1755175765061,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","url":"https://realtime.braintrustapi.com/broadcast","error":{"errno":-104,"code":"ECONNRESET","syscall":"read"},"traceId":"508a112bb36157b5a73fe289094a943a","spanId":"fc022f32393d7e6b","spanName":"POST","msg":"Error when requesting url"}
{"level":50,"time":1755175765061,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","task":"event-publisher","elapsed":0.024000167846679688,"payloadSize":847005,"error":{"errno":-104,"code":"ECONNRESET","syscall":"read"},"traceId":"508a112bb36157b5a73fe289094a943a","spanId":"fc022f32393d7e6b","spanName":"POST","msg":"Broadcast request failed: read ECONNRESET"}
{"level":50,"time":1755176240440,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","callerIdentifier":"[BrainstoreTS] [updateFrontierLoop] logs2","error":{},"traceId":"e23bd2b6448c6de0529afbe8a296d769","spanId":"eff2366b568b2233","spanName":"POST","msg":"Operation failed, continuing..."}
{"level":50,"time":1755176240440,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","callerIdentifier":"[BrainstoreTS] [updateFrontierLoop] logs","error":{},"traceId":"e23bd2b6448c6de0529afbe8a296d769","spanId":"eff2366b568b2233","spanName":"POST","msg":"Operation failed, continuing..."}
{"level":50,"time":1755177443255,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","callerIdentifier":"[BrainstoreTS] [updateFrontierLoop] logs2","error":{},"traceId":"ac40b447b350d36b2ac8d66bd586f898","spanId":"2546d154aabc0096","spanName":"POST","msg":"Operation failed, continuing..."}
{"level":50,"time":1755177443255,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","callerIdentifier":"[BrainstoreTS] [updateFrontierLoop] logs","error":{},"traceId":"ac40b447b350d36b2ac8d66bd586f898","spanId":"2546d154aabc0096","spanName":"POST","msg":"Operation failed, continuing..."}
{"level":40,"time":1755177733185,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","mergeIds":"{\"objectType\":\"experiment\",\"objectId\":\"37ffc378-8845-4573-bc86-0b844185f42c\",\"id\":\"4bd5b582-8096-43a6-83bb-31eaaa56cfaf\"}, {\"objectType\":\"experiment\",\"objectId\":\"37ffc378-8845-4573-bc86-0b844185f42c\",\"id\":\"00d0d6c5-99c7-4b16-8c83-1469c8628f4e\"}, {\"objectType\":\"experiment\",\"objectId\":\"37ffc378-8845-4573-bc86-0b844185f42c\",\"id\":\"96c51ac6-51cc-4457-b3bd-bcf100333ca5\"}","traceId":"ed51a225df694b947faeba9bdbfb98f6","spanId":"f392acaf5788c4ff","spanName":"write rows to pg","msg":"Merge advisory locks timeout"}
{"level":50,"time":1755177733185,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","error":"Timed out waiting for merge advisory locks","orgIds":["6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78"],"traceId":"ed51a225df694b947faeba9bdbfb98f6","spanId":"07bf7b16f8705b2c","spanName":"POST","msg":"Query timed out"}
{"level":40,"time":1755177733185,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"ed51a225df694b947faeba9bdbfb98f6","spanId":"07bf7b16f8705b2c","spanName":"POST","msg":"Forcibly terminating process at pid 402771"}
{"level":40,"time":1755177733197,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"ed51a225df694b947faeba9bdbfb98f6","spanId":"8b64a2fa06690e00","attributes":{"error.type":"Xu","error.status_code":200,"error_context.userId":"bca88f4c-823f-47cd-8ad9-38d95d9c03e2","error_context.userEmail":"<EMAIL>","error_context.orgName":"Microsoft Copilot PME"},"events":[{"name":"exception","attributes":{"exception.type":"Error","exception.message":"Too many concurrent insert requests","exception.stacktrace":"Error: Too many concurrent insert requests\n    at eDm (/braintrust/api-ts/local/local.js:3526:994)\n    at async j2m (/braintrust/api-ts/local/local.js:3364:8609)\n    at async lst (/braintrust/api-ts/local/local.js:3614:6279)\n    at async Q4e (/braintrust/api-ts/local/local.js:3614:6440)"},"time":[1755177733,197206599],"droppedAttributesCount":0}],"startTime":"2025-08-14T13:22:13.197Z","durationMs":"0.21","spanName":"error","msg":"error"}
{"level":30,"time":1755177733199,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","method":"POST","path":"/logs3","statusCode":429,"error":"\nXu [Error]: Too many concurrent insert requests\n    at eDm (/braintrust/api-ts/local/local.js:3526:994)\n    at async j2m (/braintrust/api-ts/local/local.js:3364:8609)\n    at async lst (/braintrust/api-ts/local/local.js:3614:6279)\n    at async Q4e (/braintrust/api-ts/local/local.js:3614:6440) {\n  status: 429\n}","errorContext":{"userId":"bca88f4c-823f-47cd-8ad9-38d95d9c03e2","userEmail":"<EMAIL>","orgName":"Microsoft Copilot PME"},"traceId":"ed51a225df694b947faeba9bdbfb98f6","spanId":"07bf7b16f8705b2c","spanName":"POST","msg":"REQUEST WILL FAIL: Too many concurrent insert requests"}
{"level":50,"time":1755178044392,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","callerIdentifier":"[BrainstoreTS] [updateFrontierLoop] logs","error":{},"traceId":"3d8e53c3b562b9bdae28684d730750d0","spanId":"8a727a4d562e47d4","spanName":"POST","msg":"Operation failed, continuing..."}
{"level":50,"time":1755178044529,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","callerIdentifier":"[BrainstoreTS] [updateFrontierLoop] logs2","error":{},"traceId":"3d8e53c3b562b9bdae28684d730750d0","spanId":"8a727a4d562e47d4","spanName":"POST","msg":"Operation failed, continuing..."}
{"level":50,"time":1755179199600,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","errors":"Generic MicrosoftAzure error: Error performing GET https://btstoragexl81cs.blob.core.windows.net/brainstore/brainstore/wal/object-store-objects/5779d0d2-d036-4335-89a9-b4909bf966ae/experiment:0371b4a6-04a1-4245-a201-53764bc34954/1000195619673914899.2e97bd3e-d843-4042-b20f-7c2948a8eee2.jsonl in 1.361930194s - HTTP error: error sending request","traceId":"c4ef4f9a07669ad104e6678ea0893d46","spanId":"a2bd0a14ab4b1b06","spanName":"POST","msg":"Failed to run BrainstoreQuery"}
{"level":40,"time":1755179199887,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"c4ef4f9a07669ad104e6678ea0893d46","spanId":"b2e890b4a5ec1333","attributes":{"error.type":"Kt","error.status_code":200,"error_context.userId":"d30b92da-a28c-46be-b489-996fa396c82b","error_context.userEmail":"<EMAIL>"},"events":[{"name":"exception","attributes":{"exception.type":"Error","exception.message":"Failed to run BrainstoreQuery: Generic MicrosoftAzure error: Error performing GET https://btstoragexl81cs.blob.core.windows.net/brainstore/brainstore/wal/object-store-objects/5779d0d2-d036-4335-89a9-b4909bf966ae/experiment:0371b4a6-04a1-4245-a201-53764bc34954/1000195619673914899.2e97bd3e-d843-4042-b20f-7c2948a8eee2.jsonl in 1.361930194s - HTTP error: error sending request","exception.stacktrace":"Error: Failed to run BrainstoreQuery: Generic MicrosoftAzure error: Error performing GET https://btstoragexl81cs.blob.core.windows.net/brainstore/brainstore/wal/object-store-objects/5779d0d2-d036-4335-89a9-b4909bf966ae/experiment:0371b4a6-04a1-4245-a201-53764bc34954/1000195619673914899.2e97bd3e-d843-4042-b20f-7c2948a8eee2.jsonl in 1.361930194s - HTTP error: error sending request\n    at SIo (/braintrust/api-ts/local/local.js:1462:38)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async IR (/braintrust/api-ts/local/local.js:2288:5803)\n    at async qbn (/braintrust/api-ts/local/local.js:2287:19284)\n    at async ZDm (/braintrust/api-ts/local/local.js:3614:14306)"},"time":[1755179199,886256960],"droppedAttributesCount":0}],"startTime":"2025-08-14T13:46:39.886Z","durationMs":"0.26","spanName":"error","msg":"error"}
{"level":50,"time":1755179199888,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","method":"POST","path":"/btql","statusCode":500,"error":"\nKt [Error]: Failed to run BrainstoreQuery: Generic MicrosoftAzure error: Error performing GET https://btstoragexl81cs.blob.core.windows.net/brainstore/brainstore/wal/object-store-objects/5779d0d2-d036-4335-89a9-b4909bf966ae/experiment:0371b4a6-04a1-4245-a201-53764bc34954/1000195619673914899.2e97bd3e-d843-4042-b20f-7c2948a8eee2.jsonl in 1.361930194s - HTTP error: error sending request\n    at SIo (/braintrust/api-ts/local/local.js:1462:38)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async IR (/braintrust/api-ts/local/local.js:2288:5803)\n    at async qbn (/braintrust/api-ts/local/local.js:2287:19284)\n    at async ZDm (/braintrust/api-ts/local/local.js:3614:14306)","errorContext":{"userId":"d30b92da-a28c-46be-b489-996fa396c82b","userEmail":"<EMAIL>","orgName":null},"traceId":"c4ef4f9a07669ad104e6678ea0893d46","spanId":"a2bd0a14ab4b1b06","spanName":"POST","msg":"REQUEST WILL FAIL ( INTERNAL SERVER ERROR ): Failed to run BrainstoreQuery: Generic MicrosoftAzure error: Error performing GET https://btstoragexl81cs.blob.core.windows.net/brainstore/brainstore/wal/object-store-objects/5779d0d2-d036-4335-89a9-b4909bf966ae/experiment:0371b4a6-04a1-4245-a201-53764bc34954/1000195619673914899.2e97bd3e-d843-4042-b20f-7c2948a8eee2.jsonl in 1.361930194s - HTTP error: error sending request"}
{"level":40,"time":1755179199892,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"c4ef4f9a07669ad104e6678ea0893d46","spanId":"a2bd0a14ab4b1b06","attributes":{"http.url":"http://mangoeastus2prodcpu.inf5mi6t.com/btql","http.host":"mangoeastus2prodcpu.inf5mi6t.com","net.host.name":"mangoeastus2prodcpu.inf5mi6t.com","http.method":"POST","http.scheme":"http","http.client_ip":"************","http.target":"/btql","http.user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","http.request_content_length_uncompressed":1897,"http.flavor":"1.1","net.transport":"ip_tcp","commit":"23f4f06557fae20cfa21f97164e90a74a7000550","net.host.ip":"************","net.host.port":8000,"net.peer.ip":"************","net.peer.port":57736,"http.status_code":500,"http.status_text":"INTERNAL SERVER ERROR"},"events":[],"startTime":"2025-08-14T13:46:35.862Z","durationMs":"4029.68","spanName":"POST","msg":"POST"}
{"level":50,"time":1755179848691,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","callerIdentifier":"[BrainstoreTS] [updateFrontierLoop] logs","error":{},"traceId":"d5dce989ac143e60bdf45d88419ed0a0","spanId":"58d5801d49f29292","spanName":"POST","msg":"Operation failed, continuing..."}
{"level":50,"time":1755179848792,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","callerIdentifier":"[BrainstoreTS] [updateFrontierLoop] logs2","error":{},"traceId":"d5dce989ac143e60bdf45d88419ed0a0","spanId":"58d5801d49f29292","spanName":"POST","msg":"Operation failed, continuing..."}
{"level":40,"time":1755180344845,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"c36f597baee0cd5884a0f3d262bcdc91","spanId":"bf4b0833c1b54fc6","attributes":{"http.request.method":"POST","http.request.method_original":"POST","url.full":"https://realtime.braintrustapi.com/broadcast","url.path":"/broadcast","url.query":"","url.scheme":"https","server.address":"realtime.braintrustapi.com","server.port":443,"network.peer.address":"***********","network.peer.port":443},"events":[{"name":"exception","attributes":{"exception.type":"ECONNRESET","exception.message":"read ECONNRESET","exception.stacktrace":"Error: read ECONNRESET\n    at TLSWrap.onStreamRead (node:internal/stream_base_commons:216:20)\n    at TLSWrap.callbackTrampoline (node:internal/async_hooks:130:17)"},"time":[1755180344,844563704],"droppedAttributesCount":0}],"startTime":"2025-08-14T14:05:44.689Z","durationMs":"155.57","spanName":"POST","msg":"POST"}
{"level":50,"time":1755180344845,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","url":"https://realtime.braintrustapi.com/broadcast","error":{"errno":-104,"code":"ECONNRESET","syscall":"read"},"traceId":"4e6df8bffc9cab7fbcbcee1c8450b588","spanId":"1c81520b0d064cf0","spanName":"POST","msg":"Error when requesting url"}
{"level":50,"time":1755180344845,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","task":"event-publisher","elapsed":0.1700000762939453,"payloadSize":3780373,"error":{"errno":-104,"code":"ECONNRESET","syscall":"read"},"traceId":"4e6df8bffc9cab7fbcbcee1c8450b588","spanId":"1c81520b0d064cf0","spanName":"POST","msg":"Broadcast request failed: read ECONNRESET"}
{"level":40,"time":1755180345247,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"f46ce5cdeb2a38c35bf28289422931e5","spanId":"6c33462c7eb187c5","attributes":{"http.request.method":"POST","http.request.method_original":"POST","url.full":"https://realtime.braintrustapi.com/broadcast","url.path":"/broadcast","url.query":"","url.scheme":"https","server.address":"realtime.braintrustapi.com","server.port":443,"network.peer.address":"***********","network.peer.port":443},"events":[{"name":"exception","attributes":{"exception.type":"ECONNRESET","exception.message":"read ECONNRESET","exception.stacktrace":"Error: read ECONNRESET\n    at TLSWrap.onStreamRead (node:internal/stream_base_commons:216:20)\n    at TLSWrap.callbackTrampoline (node:internal/async_hooks:130:17)"},"time":[1755180345,247734902],"droppedAttributesCount":0}],"startTime":"2025-08-14T14:05:45.218Z","durationMs":"29.74","spanName":"POST","msg":"POST"}
{"level":50,"time":1755180345248,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","url":"https://realtime.braintrustapi.com/broadcast","error":{"errno":-104,"code":"ECONNRESET","syscall":"read"},"traceId":"df929d82bdcfef50d9c841778a98dddd","spanId":"d49d2141ca7cf7e2","spanName":"POST","msg":"Error when requesting url"}
{"level":50,"time":1755180345248,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","task":"event-publisher","elapsed":0.03099989891052246,"payloadSize":99481,"error":{"errno":-104,"code":"ECONNRESET","syscall":"read"},"traceId":"df929d82bdcfef50d9c841778a98dddd","spanId":"d49d2141ca7cf7e2","spanName":"POST","msg":"Broadcast request failed: read ECONNRESET"}
{"level":50,"time":1755180449999,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","callerIdentifier":"[BrainstoreTS] [updateFrontierLoop] logs","error":{},"traceId":"3cb59e0a1287d06070ca7ebdaed47b99","spanId":"27a15dd41e646a86","spanName":"POST","msg":"Operation failed, continuing..."}
{"level":50,"time":1755180449999,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","callerIdentifier":"[BrainstoreTS] [updateFrontierLoop] logs2","error":{},"traceId":"3cb59e0a1287d06070ca7ebdaed47b99","spanId":"27a15dd41e646a86","spanName":"POST","msg":"Operation failed, continuing..."}
{"level":50,"time":1755181051191,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","callerIdentifier":"[BrainstoreTS] [updateFrontierLoop] logs","error":{},"traceId":"8d786eec3731ea8072e60f9182ebad8e","spanId":"0149d675fa3cfb18","spanName":"POST","msg":"Operation failed, continuing..."}
{"level":50,"time":1755181051191,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","callerIdentifier":"[BrainstoreTS] [updateFrontierLoop] logs2","error":{},"traceId":"8d786eec3731ea8072e60f9182ebad8e","spanId":"0149d675fa3cfb18","spanName":"POST","msg":"Operation failed, continuing..."}
{"level":40,"time":1755181104459,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"d954399d1e2d383a5af1d53b24f2aec0","spanId":"e1847e0a301a540d","attributes":{"http.request.method":"POST","http.request.method_original":"POST","url.full":"http://brainstore.braintrust:4000/btql/query","url.path":"/btql/query","url.query":"","url.scheme":"http","server.address":"brainstore.braintrust","server.port":4000,"user_agent.original":"undici","network.peer.address":"************","network.peer.port":4000},"events":[{"name":"exception","attributes":{"exception.type":"UND_ERR_SOCKET","exception.message":"other side closed","exception.stacktrace":"SocketError: other side closed\n    at Socket.<anonymous> (/braintrust/api-ts/local/local.js:695:11694)\n    at Socket.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)"},"time":[1755181104,458367275],"droppedAttributesCount":0}],"startTime":"2025-08-14T14:18:24.423Z","durationMs":"35.37","spanName":"brainstore","msg":"POST"}
{"level":50,"time":1755181104459,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","path":"btql/query","elapsedMs":36,"error":"fetch failed","traceId":"8bdf736479914042cbb3727336731d08","spanId":"b1ada53bce64c4c1","spanName":"brainstore","msg":"[BrainstoreTS] failed to run btql/query request after 36ms: fetch failed"}
{"level":40,"time":1755181104460,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","attempt":1,"maxRetries":3,"error":{},"sleepTimeMs":100,"traceId":"8bdf736479914042cbb3727336731d08","spanId":"02c1dd0aea006e88","spanName":"brainstore_query","msg":"Failed to run BrainstoreQuery, retrying"}
{"level":40,"time":1755181460451,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","mergeIds":"{\"objectType\":\"experiment\",\"objectId\":\"b8df627e-e772-4141-8fd3-512ba6e7e032\",\"id\":\"bb97a90e-ea57-4701-bc96-34590c85a3e1\"}, {\"objectType\":\"experiment\",\"objectId\":\"b8df627e-e772-4141-8fd3-512ba6e7e032\",\"id\":\"a29631bc-e384-4b46-80ca-7a7df13b2ea2\"}, {\"objectType\":\"experiment\",\"objectId\":\"b8df627e-e772-4141-8fd3-512ba6e7e032\",\"id\":\"164f4a90-953c-42fb-87f5-8ced9969605c\"}","traceId":"d8e8bdc7d919afd5597eceb1e36386c6","spanId":"7e6d74a6f8e28401","spanName":"write rows to pg","msg":"Merge advisory locks timeout"}
{"level":50,"time":1755181460451,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","error":"Timed out waiting for merge advisory locks","orgIds":["6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78"],"traceId":"d8e8bdc7d919afd5597eceb1e36386c6","spanId":"d03a3505eb3379a0","spanName":"POST","msg":"Query timed out"}
{"level":40,"time":1755181460451,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"d8e8bdc7d919afd5597eceb1e36386c6","spanId":"d03a3505eb3379a0","spanName":"POST","msg":"Forcibly terminating process at pid 428280"}
{"level":40,"time":1755181460521,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"d8e8bdc7d919afd5597eceb1e36386c6","spanId":"cdb3f1000c3258e5","attributes":{"error.type":"Xu","error.status_code":200,"error_context.userId":"30aeedd6-9242-49b5-8699-6cf47a2d3abe","error_context.userEmail":"<EMAIL>","error_context.orgName":"Microsoft Copilot PME"},"events":[{"name":"exception","attributes":{"exception.type":"Error","exception.message":"Too many concurrent insert requests","exception.stacktrace":"Error: Too many concurrent insert requests\n    at eDm (/braintrust/api-ts/local/local.js:3526:994)\n    at async j2m (/braintrust/api-ts/local/local.js:3364:8609)\n    at async lst (/braintrust/api-ts/local/local.js:3614:6279)\n    at async Q4e (/braintrust/api-ts/local/local.js:3614:6440)"},"time":[1755181460,521229653],"droppedAttributesCount":0}],"startTime":"2025-08-14T14:24:20.521Z","durationMs":"0.23","spanName":"error","msg":"error"}
{"level":30,"time":1755181460522,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","method":"POST","path":"/logs3","statusCode":429,"error":"\nXu [Error]: Too many concurrent insert requests\n    at eDm (/braintrust/api-ts/local/local.js:3526:994)\n    at async j2m (/braintrust/api-ts/local/local.js:3364:8609)\n    at async lst (/braintrust/api-ts/local/local.js:3614:6279)\n    at async Q4e (/braintrust/api-ts/local/local.js:3614:6440) {\n  status: 429\n}","errorContext":{"userId":"30aeedd6-9242-49b5-8699-6cf47a2d3abe","userEmail":"<EMAIL>","orgName":"Microsoft Copilot PME"},"traceId":"d8e8bdc7d919afd5597eceb1e36386c6","spanId":"d03a3505eb3379a0","spanName":"POST","msg":"REQUEST WILL FAIL: Too many concurrent insert requests"}
{"level":40,"time":1755182904140,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","mergeIds":"{\"objectType\":\"experiment\",\"objectId\":\"c9d4e441-c279-477b-9a8d-463a7e3036a6\",\"id\":\"712af45c-aa08-494a-a25c-94ba333c5793\"}, {\"objectType\":\"experiment\",\"objectId\":\"c9d4e441-c279-477b-9a8d-463a7e3036a6\",\"id\":\"88fabbe8-fddb-4145-86ad-90c9cf132c28\"}","traceId":"706ffde6d4c1566c7c5513b1a715281f","spanId":"622bb6af6726f07b","spanName":"write rows to pg","msg":"Merge advisory locks timeout"}
{"level":50,"time":1755182904140,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","error":"Timed out waiting for merge advisory locks","orgIds":["6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78"],"traceId":"706ffde6d4c1566c7c5513b1a715281f","spanId":"ba56d98b9f453069","spanName":"POST","msg":"Query timed out"}
{"level":40,"time":1755182904141,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"706ffde6d4c1566c7c5513b1a715281f","spanId":"ba56d98b9f453069","spanName":"POST","msg":"Forcibly terminating process at pid 436526"}
{"level":40,"time":1755182904416,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"706ffde6d4c1566c7c5513b1a715281f","spanId":"cdf4ffd3f43bb219","attributes":{"error.type":"Xu","error.status_code":200,"error_context.userId":"d30b92da-a28c-46be-b489-996fa396c82b","error_context.userEmail":"<EMAIL>","error_context.orgName":"Microsoft Copilot PME"},"events":[{"name":"exception","attributes":{"exception.type":"Error","exception.message":"Too many concurrent insert requests","exception.stacktrace":"Error: Too many concurrent insert requests\n    at eDm (/braintrust/api-ts/local/local.js:3526:994)\n    at async j2m (/braintrust/api-ts/local/local.js:3364:8609)\n    at async lst (/braintrust/api-ts/local/local.js:3614:6279)\n    at async Q4e (/braintrust/api-ts/local/local.js:3614:6440)"},"time":[1755182904,416217212],"droppedAttributesCount":0}],"startTime":"2025-08-14T14:48:24.416Z","durationMs":"0.22","spanName":"error","msg":"error"}
{"level":30,"time":1755182904417,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","method":"POST","path":"/logs3","statusCode":429,"error":"\nXu [Error]: Too many concurrent insert requests\n    at eDm (/braintrust/api-ts/local/local.js:3526:994)\n    at async j2m (/braintrust/api-ts/local/local.js:3364:8609)\n    at async lst (/braintrust/api-ts/local/local.js:3614:6279)\n    at async Q4e (/braintrust/api-ts/local/local.js:3614:6440) {\n  status: 429\n}","errorContext":{"userId":"d30b92da-a28c-46be-b489-996fa396c82b","userEmail":"<EMAIL>","orgName":"Microsoft Copilot PME"},"traceId":"706ffde6d4c1566c7c5513b1a715281f","spanId":"ba56d98b9f453069","spanName":"POST","msg":"REQUEST WILL FAIL: Too many concurrent insert requests"}
{"level":40,"time":1755183070611,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","mergeIds":"{\"objectType\":\"experiment\",\"objectId\":\"33c3b939-98a1-4126-b6a3-db711d4a63b5\",\"id\":\"7d97fc80-61ae-4de7-91be-bd0169db8a23\"}, {\"objectType\":\"experiment\",\"objectId\":\"33c3b939-98a1-4126-b6a3-db711d4a63b5\",\"id\":\"a01ee1b7-9487-410b-87b3-ebdd68067e9a\"}, {\"objectType\":\"experiment\",\"objectId\":\"33c3b939-98a1-4126-b6a3-db711d4a63b5\",\"id\":\"bd88be39-2202-4d55-92c3-ae7d6a15568a\"}, {\"objectType\":\"experiment\",\"objectId\":\"33c3b939-98a1-4126-b6a3-db711d4a63b5\",\"id\":\"c3389dec-fe21-428e-9c24-87c263d8746c\"}, {\"objectType\":\"experiment\",\"objectId\":\"33c3b939-98a1-4126-b6a3-db711d4a63b5\",\"id\":\"306ca9d3-fcb7-49f4-9f57-614c0a5e1a77\"}","traceId":"957ecf1b32ee6eb4db4816f92527a199","spanId":"da8da2493ad0038a","spanName":"write rows to pg","msg":"Merge advisory locks timeout"}
{"level":50,"time":1755183070611,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","error":"Timed out waiting for merge advisory locks","orgIds":["6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78"],"traceId":"957ecf1b32ee6eb4db4816f92527a199","spanId":"1ed33c31de7975a9","spanName":"POST","msg":"Query timed out"}
{"level":40,"time":1755183070611,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"957ecf1b32ee6eb4db4816f92527a199","spanId":"1ed33c31de7975a9","spanName":"POST","msg":"Forcibly terminating process at pid 436318"}
{"level":40,"time":1755183070833,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"957ecf1b32ee6eb4db4816f92527a199","spanId":"c68f296fff15f744","attributes":{"error.type":"Xu","error.status_code":200,"error_context.userId":"bca88f4c-823f-47cd-8ad9-38d95d9c03e2","error_context.userEmail":"<EMAIL>","error_context.orgName":"Microsoft Copilot PME"},"events":[{"name":"exception","attributes":{"exception.type":"Error","exception.message":"Too many concurrent insert requests","exception.stacktrace":"Error: Too many concurrent insert requests\n    at eDm (/braintrust/api-ts/local/local.js:3526:994)\n    at async j2m (/braintrust/api-ts/local/local.js:3364:8609)\n    at async lst (/braintrust/api-ts/local/local.js:3614:6279)\n    at async Q4e (/braintrust/api-ts/local/local.js:3614:6440)"},"time":[1755183070,833227970],"droppedAttributesCount":0}],"startTime":"2025-08-14T14:51:10.833Z","durationMs":"0.23","spanName":"error","msg":"error"}
{"level":30,"time":1755183070834,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","method":"POST","path":"/logs3","statusCode":429,"error":"\nXu [Error]: Too many concurrent insert requests\n    at eDm (/braintrust/api-ts/local/local.js:3526:994)\n    at async j2m (/braintrust/api-ts/local/local.js:3364:8609)\n    at async lst (/braintrust/api-ts/local/local.js:3614:6279)\n    at async Q4e (/braintrust/api-ts/local/local.js:3614:6440) {\n  status: 429\n}","errorContext":{"userId":"bca88f4c-823f-47cd-8ad9-38d95d9c03e2","userEmail":"<EMAIL>","orgName":"Microsoft Copilot PME"},"traceId":"957ecf1b32ee6eb4db4816f92527a199","spanId":"1ed33c31de7975a9","spanName":"POST","msg":"REQUEST WILL FAIL: Too many concurrent insert requests"}
{"level":40,"time":1755183094164,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","mergeIds":"{\"objectType\":\"experiment\",\"objectId\":\"33c3b939-98a1-4126-b6a3-db711d4a63b5\",\"id\":\"7d97fc80-61ae-4de7-91be-bd0169db8a23\"}, {\"objectType\":\"experiment\",\"objectId\":\"33c3b939-98a1-4126-b6a3-db711d4a63b5\",\"id\":\"a01ee1b7-9487-410b-87b3-ebdd68067e9a\"}, {\"objectType\":\"experiment\",\"objectId\":\"33c3b939-98a1-4126-b6a3-db711d4a63b5\",\"id\":\"bd88be39-2202-4d55-92c3-ae7d6a15568a\"}, {\"objectType\":\"experiment\",\"objectId\":\"33c3b939-98a1-4126-b6a3-db711d4a63b5\",\"id\":\"c3389dec-fe21-428e-9c24-87c263d8746c\"}, {\"objectType\":\"experiment\",\"objectId\":\"33c3b939-98a1-4126-b6a3-db711d4a63b5\",\"id\":\"306ca9d3-fcb7-49f4-9f57-614c0a5e1a77\"}","traceId":"8e98eaeda6867f3c4cf282c9e7fd6e1b","spanId":"9d07ea6c0aacfae0","spanName":"write rows to pg","msg":"Merge advisory locks timeout"}
{"level":50,"time":1755183094164,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","error":"Timed out waiting for merge advisory locks","orgIds":["6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78"],"traceId":"8e98eaeda6867f3c4cf282c9e7fd6e1b","spanId":"81741b67d1573294","spanName":"POST","msg":"Query timed out"}
{"level":40,"time":1755183094164,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"8e98eaeda6867f3c4cf282c9e7fd6e1b","spanId":"81741b67d1573294","spanName":"POST","msg":"Forcibly terminating process at pid 436703"}
{"level":40,"time":1755183094173,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"8e98eaeda6867f3c4cf282c9e7fd6e1b","spanId":"673e77c3f39861e5","attributes":{"error.type":"Xu","error.status_code":200,"error_context.userId":"bca88f4c-823f-47cd-8ad9-38d95d9c03e2","error_context.userEmail":"<EMAIL>","error_context.orgName":"Microsoft Copilot PME"},"events":[{"name":"exception","attributes":{"exception.type":"Error","exception.message":"Too many concurrent insert requests","exception.stacktrace":"Error: Too many concurrent insert requests\n    at eDm (/braintrust/api-ts/local/local.js:3526:994)\n    at async j2m (/braintrust/api-ts/local/local.js:3364:8609)\n    at async lst (/braintrust/api-ts/local/local.js:3614:6279)\n    at async Q4e (/braintrust/api-ts/local/local.js:3614:6440)"},"time":[1755183094,173078355],"droppedAttributesCount":0}],"startTime":"2025-08-14T14:51:34.173Z","durationMs":"0.08","spanName":"error","msg":"error"}
{"level":30,"time":1755183094173,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","method":"POST","path":"/logs3","statusCode":429,"error":"\nXu [Error]: Too many concurrent insert requests\n    at eDm (/braintrust/api-ts/local/local.js:3526:994)\n    at async j2m (/braintrust/api-ts/local/local.js:3364:8609)\n    at async lst (/braintrust/api-ts/local/local.js:3614:6279)\n    at async Q4e (/braintrust/api-ts/local/local.js:3614:6440) {\n  status: 429\n}","errorContext":{"userId":"bca88f4c-823f-47cd-8ad9-38d95d9c03e2","userEmail":"<EMAIL>","orgName":"Microsoft Copilot PME"},"traceId":"8e98eaeda6867f3c4cf282c9e7fd6e1b","spanId":"81741b67d1573294","spanName":"POST","msg":"REQUEST WILL FAIL: Too many concurrent insert requests"}
{"level":50,"time":1755183456337,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","callerIdentifier":"[BrainstoreTS] [updateFrontierLoop] logs2","error":{},"traceId":"6bd01a1f97cf54a17ee48f05d1ab7c71","spanId":"c886f500ac821b8b","spanName":"POST","msg":"Operation failed, continuing..."}
{"level":50,"time":1755183456544,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","callerIdentifier":"[BrainstoreTS] [updateFrontierLoop] logs","error":{},"traceId":"6bd01a1f97cf54a17ee48f05d1ab7c71","spanId":"c886f500ac821b8b","spanName":"POST","msg":"Operation failed, continuing..."}
{"level":50,"time":1755184057866,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","callerIdentifier":"[BrainstoreTS] [updateFrontierLoop] logs2","error":{},"traceId":"5b2ec0ab67738121db94dbcf45feb7f2","spanId":"a271a6b8acbf7cca","spanName":"POST","msg":"Operation failed, continuing..."}
{"level":50,"time":1755184057866,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","callerIdentifier":"[BrainstoreTS] [updateFrontierLoop] logs","error":{},"traceId":"5b2ec0ab67738121db94dbcf45feb7f2","spanId":"a271a6b8acbf7cca","spanName":"POST","msg":"Operation failed, continuing..."}
{"level":50,"time":1755185260552,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","callerIdentifier":"[BrainstoreTS] [updateFrontierLoop] logs2","error":{},"traceId":"84f2c0a23033bf33297c9d3befd18bed","spanId":"f897717e2d288c2c","spanName":"POST","msg":"Operation failed, continuing..."}
{"level":50,"time":1755185260552,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","callerIdentifier":"[BrainstoreTS] [updateFrontierLoop] logs","error":{},"traceId":"84f2c0a23033bf33297c9d3befd18bed","spanId":"f897717e2d288c2c","spanName":"POST","msg":"Operation failed, continuing..."}
{"level":50,"time":1755185310040,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","errors":"Generic MicrosoftAzure error: HTTP error: request or response body error","traceId":"8fce8af6dffaebc595a5b58b98a94da5","spanId":"4613294bb2b85255","spanName":"POST","msg":"Failed to run BrainstoreQuery"}
{"level":40,"time":1755185310046,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"8fce8af6dffaebc595a5b58b98a94da5","spanId":"b11ccb10e2d85d36","attributes":{"error.type":"Kt","error.status_code":200,"error_context.userId":"0b31b0ec-c5b8-4aeb-8a76-f3fe13251a79","error_context.userEmail":"<EMAIL>"},"events":[{"name":"exception","attributes":{"exception.type":"Error","exception.message":"Failed to run BrainstoreQuery: Generic MicrosoftAzure error: HTTP error: request or response body error","exception.stacktrace":"Error: Failed to run BrainstoreQuery: Generic MicrosoftAzure error: HTTP error: request or response body error\n    at SIo (/braintrust/api-ts/local/local.js:1462:38)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async IR (/braintrust/api-ts/local/local.js:2288:5803)\n    at async qbn (/braintrust/api-ts/local/local.js:2287:19284)\n    at async ZDm (/braintrust/api-ts/local/local.js:3614:14306)"},"time":[1755185310,46260186],"droppedAttributesCount":0}],"startTime":"2025-08-14T15:28:30.046Z","durationMs":"0.26","spanName":"error","msg":"error"}
{"level":50,"time":1755185310047,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","method":"POST","path":"/btql","statusCode":500,"error":"\nKt [Error]: Failed to run BrainstoreQuery: Generic MicrosoftAzure error: HTTP error: request or response body error\n    at SIo (/braintrust/api-ts/local/local.js:1462:38)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async IR (/braintrust/api-ts/local/local.js:2288:5803)\n    at async qbn (/braintrust/api-ts/local/local.js:2287:19284)\n    at async ZDm (/braintrust/api-ts/local/local.js:3614:14306)","errorContext":{"userId":"0b31b0ec-c5b8-4aeb-8a76-f3fe13251a79","userEmail":"<EMAIL>","orgName":null},"traceId":"8fce8af6dffaebc595a5b58b98a94da5","spanId":"4613294bb2b85255","spanName":"POST","msg":"REQUEST WILL FAIL ( INTERNAL SERVER ERROR ): Failed to run BrainstoreQuery: Generic MicrosoftAzure error: HTTP error: request or response body error"}
{"level":40,"time":1755185310060,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"8fce8af6dffaebc595a5b58b98a94da5","spanId":"4613294bb2b85255","attributes":{"http.url":"http://mangoeastus2prodcpu.inf5mi6t.com/btql","http.host":"mangoeastus2prodcpu.inf5mi6t.com","net.host.name":"mangoeastus2prodcpu.inf5mi6t.com","http.method":"POST","http.scheme":"http","http.client_ip":"************","http.target":"/btql","http.user_agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0","http.request_content_length_uncompressed":6557,"http.flavor":"1.1","net.transport":"ip_tcp","commit":"23f4f06557fae20cfa21f97164e90a74a7000550","net.host.ip":"************","net.host.port":8000,"net.peer.ip":"*************","net.peer.port":33974,"http.status_code":500,"http.status_text":"INTERNAL SERVER ERROR"},"events":[],"startTime":"2025-08-14T15:28:28.194Z","durationMs":"1866.41","spanName":"POST","msg":"POST"}
{"level":40,"time":1755185643425,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"a6a1b6025152bee3d9bdc05cf11ec8f3","spanId":"a2761b35317b8228","attributes":{"http.request.method":"POST","http.request.method_original":"POST","url.full":"http://brainstore.braintrust:4000/btql/query","url.path":"/btql/query","url.query":"","url.scheme":"http","server.address":"brainstore.braintrust","server.port":4000,"user_agent.original":"undici","network.peer.address":"************","network.peer.port":4000},"events":[{"name":"exception","attributes":{"exception.type":"UND_ERR_SOCKET","exception.message":"other side closed","exception.stacktrace":"SocketError: other side closed\n    at Socket.<anonymous> (/braintrust/api-ts/local/local.js:695:11694)\n    at Socket.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)"},"time":[1755185643,425151411],"droppedAttributesCount":0}],"startTime":"2025-08-14T15:33:58.221Z","durationMs":"5204.16","spanName":"brainstore","msg":"POST"}
{"level":50,"time":1755185643425,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","path":"btql/query","elapsedMs":5205,"error":"fetch failed","traceId":"5a288841ebad4d00af95903c974b3ba6","spanId":"1c6e0d4d54beb3d1","spanName":"brainstore","msg":"[BrainstoreTS] failed to run btql/query request after 5205ms: fetch failed"}
{"level":40,"time":1755185643977,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"5a288841ebad4d00af95903c974b3ba6","spanId":"a61edf8cdafc67c5","attributes":{"error.type":"TypeError","error.status_code":200,"error_context.userId":"30aeedd6-9242-49b5-8699-6cf47a2d3abe","error_context.userEmail":"<EMAIL>"},"events":[{"name":"exception","attributes":{"exception.type":"TypeError","exception.message":"fetch failed","exception.stacktrace":"TypeError: fetch failed\n    at as.exports.fetch (/braintrust/api-ts/local/local.js:727:6843)\n    at async d (/braintrust/api-ts/local/local.js:1463:4281)\n    at async /braintrust/api-ts/local/local.js:1029:109951\n    at async wIo (/braintrust/api-ts/local/local.js:1463:4974)\n    at async /braintrust/api-ts/local/local.js:1463:931\n    at async /braintrust/api-ts/local/local.js:1029:109951\n    at async f_p (/braintrust/api-ts/local/local.js:1463:844)\n    at async /braintrust/api-ts/local/local.js:1461:4456\n    at async /braintrust/api-ts/local/local.js:1029:109951\n    at async SIo (/braintrust/api-ts/local/local.js:1461:4385)"},"time":[1755185643,977141617],"droppedAttributesCount":0}],"startTime":"2025-08-14T15:34:03.977Z","durationMs":"0.14","spanName":"error","msg":"error"}
{"level":50,"time":1755185643979,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","method":"POST","path":"/btql","statusCode":500,"error":"\nTypeError: fetch failed\n    at as.exports.fetch (/braintrust/api-ts/local/local.js:727:6843)\n    at async d (/braintrust/api-ts/local/local.js:1463:4281)\n    at async /braintrust/api-ts/local/local.js:1029:109951\n    at async wIo (/braintrust/api-ts/local/local.js:1463:4974)\n    at async /braintrust/api-ts/local/local.js:1463:931\n    at async /braintrust/api-ts/local/local.js:1029:109951\n    at async f_p (/braintrust/api-ts/local/local.js:1463:844)\n    at async /braintrust/api-ts/local/local.js:1461:4456\n    at async /braintrust/api-ts/local/local.js:1029:109951\n    at async SIo (/braintrust/api-ts/local/local.js:1461:4385) {\n  [cause]: ZXr [SocketError]: other side closed\n      at Socket.<anonymous> (/braintrust/api-ts/local/local.js:695:11694)\n      at Socket.emit (node:events:530:35)\n      at endReadableNT (node:internal/streams/readable:1698:12)\n      at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {\n    code: 'UND_ERR_SOCKET',\n    socket: {\n      localAddress: '************',\n      localPort: 47514,\n      remoteAddress: '************',\n      remotePort: 4000,\n      remoteFamily: 'IPv4',\n      timeout: undefined,\n      bytesWritten: 84295009,\n      bytesRead: 102133816\n    }\n  }\n}","errorContext":{"userId":"30aeedd6-9242-49b5-8699-6cf47a2d3abe","userEmail":"<EMAIL>","orgName":null},"traceId":"5a288841ebad4d00af95903c974b3ba6","spanId":"4b4dcff431804940","spanName":"POST","msg":"REQUEST WILL FAIL ( INTERNAL SERVER ERROR ): fetch failed"}
{"level":40,"time":1755185643987,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"5a288841ebad4d00af95903c974b3ba6","spanId":"4b4dcff431804940","attributes":{"http.url":"http://mangoeastus2prodcpu.inf5mi6t.com/btql","http.host":"mangoeastus2prodcpu.inf5mi6t.com","net.host.name":"mangoeastus2prodcpu.inf5mi6t.com","http.method":"POST","http.scheme":"http","http.client_ip":"************","http.target":"/btql","http.user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","http.request_content_length_uncompressed":18682,"http.flavor":"1.1","net.transport":"ip_tcp","commit":"23f4f06557fae20cfa21f97164e90a74a7000550","net.host.ip":"************","net.host.port":8000,"net.peer.ip":"*************","net.peer.port":37562,"http.status_code":500,"http.status_text":"INTERNAL SERVER ERROR"},"events":[],"startTime":"2025-08-14T15:33:54.909Z","durationMs":"9077.31","spanName":"POST","msg":"POST"}
{"level":40,"time":1755185734550,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"a358ceae0d9b4089147aed75b6f20b5e","spanId":"c1125344d3737014","attributes":{"error.type":"Wn","error.status_code":200,"error_context.userId":"45a9ab87-6146-4d90-9746-6c8d669c8a60","error_context.userEmail":"<EMAIL>"},"events":[{"name":"exception","attributes":{"exception.type":"Error","exception.message":"Missing read access to experiment id , or the experiment does not exist","exception.stacktrace":"Error: Missing read access to experiment id , or the experiment does not exist\n    at t.checkAndGetMulti (/braintrust/api-ts/local/local.js:1257:96)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async IR (/braintrust/api-ts/local/local.js:2288:2449)\n    at async qbn (/braintrust/api-ts/local/local.js:2287:19284)\n    at async ZDm (/braintrust/api-ts/local/local.js:3614:14306)"},"time":[1755185734,549226945],"droppedAttributesCount":0}],"startTime":"2025-08-14T15:35:34.549Z","durationMs":"0.23","spanName":"error","msg":"error"}
{"level":30,"time":1755185734551,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","method":"POST","path":"/btql","statusCode":403,"error":"\nWn [Error]: Missing read access to experiment id , or the experiment does not exist\n    at t.checkAndGetMulti (/braintrust/api-ts/local/local.js:1257:96)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async IR (/braintrust/api-ts/local/local.js:2288:2449)\n    at async qbn (/braintrust/api-ts/local/local.js:2287:19284)\n    at async ZDm (/braintrust/api-ts/local/local.js:3614:14306)","errorContext":{"userId":"45a9ab87-6146-4d90-9746-6c8d669c8a60","userEmail":"<EMAIL>","orgName":null},"traceId":"a358ceae0d9b4089147aed75b6f20b5e","spanId":"1e2e9379ea56eb24","spanName":"POST","msg":"REQUEST WILL FAIL: Missing read access to experiment id , or the experiment does not exist"}
{"level":40,"time":1755185735439,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"4eddded8112a2a2942281a3b27f3bee9","spanId":"b4778b43944ec88d","attributes":{"error.type":"Wn","error.status_code":200,"error_context.userId":"45a9ab87-6146-4d90-9746-6c8d669c8a60","error_context.userEmail":"<EMAIL>"},"events":[{"name":"exception","attributes":{"exception.type":"Error","exception.message":"Missing read access to experiment id , or the experiment does not exist","exception.stacktrace":"Error: Missing read access to experiment id , or the experiment does not exist\n    at t.checkAndGetMulti (/braintrust/api-ts/local/local.js:1257:96)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async IR (/braintrust/api-ts/local/local.js:2288:2449)\n    at async qbn (/braintrust/api-ts/local/local.js:2287:19284)\n    at async ZDm (/braintrust/api-ts/local/local.js:3614:14306)"},"time":[1755185735,439093591],"droppedAttributesCount":0}],"startTime":"2025-08-14T15:35:35.439Z","durationMs":"0.09","spanName":"error","msg":"error"}
{"level":30,"time":1755185735439,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","method":"POST","path":"/btql","statusCode":403,"error":"\nWn [Error]: Missing read access to experiment id , or the experiment does not exist\n    at t.checkAndGetMulti (/braintrust/api-ts/local/local.js:1257:96)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async IR (/braintrust/api-ts/local/local.js:2288:2449)\n    at async qbn (/braintrust/api-ts/local/local.js:2287:19284)\n    at async ZDm (/braintrust/api-ts/local/local.js:3614:14306)","errorContext":{"userId":"45a9ab87-6146-4d90-9746-6c8d669c8a60","userEmail":"<EMAIL>","orgName":null},"traceId":"4eddded8112a2a2942281a3b27f3bee9","spanId":"69e13e4a4b7db3e1","spanName":"POST","msg":"REQUEST WILL FAIL: Missing read access to experiment id , or the experiment does not exist"}
{"level":40,"time":1755185857848,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","mergeIds":"{\"objectType\":\"experiment\",\"objectId\":\"99061f7c-cb5b-4b16-92e6-5911d122bb4d\",\"id\":\"f56ed506-1016-4e4b-b851-81d9a012b579\"}, {\"objectType\":\"experiment\",\"objectId\":\"99061f7c-cb5b-4b16-92e6-5911d122bb4d\",\"id\":\"4e27804c-7c8d-4f78-bb0f-7f6b4e9484fb\"}, {\"objectType\":\"experiment\",\"objectId\":\"99061f7c-cb5b-4b16-92e6-5911d122bb4d\",\"id\":\"f2469d56-0fbf-42f7-b190-63e0fb8068f9\"}, {\"objectType\":\"experiment\",\"objectId\":\"99061f7c-cb5b-4b16-92e6-5911d122bb4d\",\"id\":\"a1acf27e-421f-4934-9330-d1ff0d6d4974\"}","traceId":"0f6c8f5eec74991520b84d0171f556c8","spanId":"9b2f9981f5a7808c","spanName":"write rows to pg","msg":"Merge advisory locks timeout"}
{"level":50,"time":1755185857849,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","error":"Timed out waiting for merge advisory locks","orgIds":["6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78"],"traceId":"0f6c8f5eec74991520b84d0171f556c8","spanId":"c9c501b569fd6283","spanName":"POST","msg":"Query timed out"}
{"level":40,"time":1755185857849,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"0f6c8f5eec74991520b84d0171f556c8","spanId":"c9c501b569fd6283","spanName":"POST","msg":"Forcibly terminating process at pid 446516"}
{"level":40,"time":1755185857857,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"0f6c8f5eec74991520b84d0171f556c8","spanId":"e6660b93fc194274","attributes":{"error.type":"Xu","error.status_code":200,"error_context.userId":"d30b92da-a28c-46be-b489-996fa396c82b","error_context.userEmail":"<EMAIL>","error_context.orgName":"Microsoft Copilot PME"},"events":[{"name":"exception","attributes":{"exception.type":"Error","exception.message":"Too many concurrent insert requests","exception.stacktrace":"Error: Too many concurrent insert requests\n    at eDm (/braintrust/api-ts/local/local.js:3526:994)\n    at async j2m (/braintrust/api-ts/local/local.js:3364:8609)\n    at async lst (/braintrust/api-ts/local/local.js:3614:6279)\n    at async Q4e (/braintrust/api-ts/local/local.js:3614:6440)"},"time":[1755185857,857230385],"droppedAttributesCount":0}],"startTime":"2025-08-14T15:37:37.857Z","durationMs":"0.23","spanName":"error","msg":"error"}
{"level":30,"time":1755185857858,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","method":"POST","path":"/logs3","statusCode":429,"error":"\nXu [Error]: Too many concurrent insert requests\n    at eDm (/braintrust/api-ts/local/local.js:3526:994)\n    at async j2m (/braintrust/api-ts/local/local.js:3364:8609)\n    at async lst (/braintrust/api-ts/local/local.js:3614:6279)\n    at async Q4e (/braintrust/api-ts/local/local.js:3614:6440) {\n  status: 429\n}","errorContext":{"userId":"d30b92da-a28c-46be-b489-996fa396c82b","userEmail":"<EMAIL>","orgName":"Microsoft Copilot PME"},"traceId":"0f6c8f5eec74991520b84d0171f556c8","spanId":"c9c501b569fd6283","spanName":"POST","msg":"REQUEST WILL FAIL: Too many concurrent insert requests"}
{"level":50,"time":1755185861885,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","callerIdentifier":"[BrainstoreTS] [updateFrontierLoop] logs2","error":{},"traceId":"d6ef0d9c0d65680ed3b1c81027945ff2","spanId":"c44b191bdc9f595e","spanName":"POST","msg":"Operation failed, continuing..."}
{"level":50,"time":1755185861886,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","callerIdentifier":"[BrainstoreTS] [updateFrontierLoop] logs","error":{},"traceId":"d6ef0d9c0d65680ed3b1c81027945ff2","spanId":"c44b191bdc9f595e","spanName":"POST","msg":"Operation failed, continuing..."}
{"level":40,"time":1755185881598,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","mergeIds":"{\"objectType\":\"experiment\",\"objectId\":\"99061f7c-cb5b-4b16-92e6-5911d122bb4d\",\"id\":\"f56ed506-1016-4e4b-b851-81d9a012b579\"}, {\"objectType\":\"experiment\",\"objectId\":\"99061f7c-cb5b-4b16-92e6-5911d122bb4d\",\"id\":\"4e27804c-7c8d-4f78-bb0f-7f6b4e9484fb\"}, {\"objectType\":\"experiment\",\"objectId\":\"99061f7c-cb5b-4b16-92e6-5911d122bb4d\",\"id\":\"f2469d56-0fbf-42f7-b190-63e0fb8068f9\"}, {\"objectType\":\"experiment\",\"objectId\":\"99061f7c-cb5b-4b16-92e6-5911d122bb4d\",\"id\":\"a1acf27e-421f-4934-9330-d1ff0d6d4974\"}","traceId":"cf1e75ba5ccccdbcf9f3d8d4f6a3a8a7","spanId":"8adec27d6bb15f14","spanName":"write rows to pg","msg":"Merge advisory locks timeout"}
{"level":50,"time":1755185881598,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","error":"Timed out waiting for merge advisory locks","orgIds":["6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78"],"traceId":"cf1e75ba5ccccdbcf9f3d8d4f6a3a8a7","spanId":"5fa08b7c4dbaf66c","spanName":"POST","msg":"Query timed out"}
{"level":40,"time":1755185881598,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"cf1e75ba5ccccdbcf9f3d8d4f6a3a8a7","spanId":"5fa08b7c4dbaf66c","spanName":"POST","msg":"Forcibly terminating process at pid 453340"}
{"level":40,"time":1755185881601,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"cf1e75ba5ccccdbcf9f3d8d4f6a3a8a7","spanId":"c13c41f10e123a8e","attributes":{"error.type":"Xu","error.status_code":200,"error_context.userId":"d30b92da-a28c-46be-b489-996fa396c82b","error_context.userEmail":"<EMAIL>","error_context.orgName":"Microsoft Copilot PME"},"events":[{"name":"exception","attributes":{"exception.type":"Error","exception.message":"Too many concurrent insert requests","exception.stacktrace":"Error: Too many concurrent insert requests\n    at eDm (/braintrust/api-ts/local/local.js:3526:994)\n    at async j2m (/braintrust/api-ts/local/local.js:3364:8609)\n    at async lst (/braintrust/api-ts/local/local.js:3614:6279)\n    at async Q4e (/braintrust/api-ts/local/local.js:3614:6440)"},"time":[1755185881,601089378],"droppedAttributesCount":0}],"startTime":"2025-08-14T15:38:01.601Z","durationMs":"0.09","spanName":"error","msg":"error"}
{"level":30,"time":1755185881601,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","method":"POST","path":"/logs3","statusCode":429,"error":"\nXu [Error]: Too many concurrent insert requests\n    at eDm (/braintrust/api-ts/local/local.js:3526:994)\n    at async j2m (/braintrust/api-ts/local/local.js:3364:8609)\n    at async lst (/braintrust/api-ts/local/local.js:3614:6279)\n    at async Q4e (/braintrust/api-ts/local/local.js:3614:6440) {\n  status: 429\n}","errorContext":{"userId":"d30b92da-a28c-46be-b489-996fa396c82b","userEmail":"<EMAIL>","orgName":"Microsoft Copilot PME"},"traceId":"cf1e75ba5ccccdbcf9f3d8d4f6a3a8a7","spanId":"5fa08b7c4dbaf66c","spanName":"POST","msg":"REQUEST WILL FAIL: Too many concurrent insert requests"}
{"level":40,"time":1755186112719,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"ae1802239b05ae43a175e52842d7f6f1","spanId":"fa246093f1aed019","attributes":{"error.type":"Wn","error.status_code":200,"error_context.userId":"45a9ab87-6146-4d90-9746-6c8d669c8a60","error_context.userEmail":"<EMAIL>"},"events":[{"name":"exception","attributes":{"exception.type":"Error","exception.message":"Missing read access to experiment id , or the experiment does not exist","exception.stacktrace":"Error: Missing read access to experiment id , or the experiment does not exist\n    at t.checkAndGetMulti (/braintrust/api-ts/local/local.js:1257:96)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async IR (/braintrust/api-ts/local/local.js:2288:2449)\n    at async qbn (/braintrust/api-ts/local/local.js:2287:19284)\n    at async ZDm (/braintrust/api-ts/local/local.js:3614:14306)"},"time":[1755186112,719229940],"droppedAttributesCount":0}],"startTime":"2025-08-14T15:41:52.719Z","durationMs":"0.23","spanName":"error","msg":"error"}
{"level":30,"time":1755186112720,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","method":"POST","path":"/btql","statusCode":403,"error":"\nWn [Error]: Missing read access to experiment id , or the experiment does not exist\n    at t.checkAndGetMulti (/braintrust/api-ts/local/local.js:1257:96)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async IR (/braintrust/api-ts/local/local.js:2288:2449)\n    at async qbn (/braintrust/api-ts/local/local.js:2287:19284)\n    at async ZDm (/braintrust/api-ts/local/local.js:3614:14306)","errorContext":{"userId":"45a9ab87-6146-4d90-9746-6c8d669c8a60","userEmail":"<EMAIL>","orgName":null},"traceId":"ae1802239b05ae43a175e52842d7f6f1","spanId":"ab7178cd14cea02f","spanName":"POST","msg":"REQUEST WILL FAIL: Missing read access to experiment id , or the experiment does not exist"}
{"level":40,"time":1755186113139,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"60f185dff4ce259d8c664c30c62d977d","spanId":"293996d4fc55dec8","attributes":{"error.type":"Wn","error.status_code":200,"error_context.userId":"45a9ab87-6146-4d90-9746-6c8d669c8a60","error_context.userEmail":"<EMAIL>"},"events":[{"name":"exception","attributes":{"exception.type":"Error","exception.message":"Missing read access to experiment id , or the experiment does not exist","exception.stacktrace":"Error: Missing read access to experiment id , or the experiment does not exist\n    at t.checkAndGetMulti (/braintrust/api-ts/local/local.js:1257:96)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async IR (/braintrust/api-ts/local/local.js:2288:2449)\n    at async qbn (/braintrust/api-ts/local/local.js:2287:19284)\n    at async ZDm (/braintrust/api-ts/local/local.js:3614:14306)"},"time":[1755186113,139076949],"droppedAttributesCount":0}],"startTime":"2025-08-14T15:41:53.139Z","durationMs":"0.08","spanName":"error","msg":"error"}
{"level":30,"time":1755186113139,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","method":"POST","path":"/btql","statusCode":403,"error":"\nWn [Error]: Missing read access to experiment id , or the experiment does not exist\n    at t.checkAndGetMulti (/braintrust/api-ts/local/local.js:1257:96)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async IR (/braintrust/api-ts/local/local.js:2288:2449)\n    at async qbn (/braintrust/api-ts/local/local.js:2287:19284)\n    at async ZDm (/braintrust/api-ts/local/local.js:3614:14306)","errorContext":{"userId":"45a9ab87-6146-4d90-9746-6c8d669c8a60","userEmail":"<EMAIL>","orgName":null},"traceId":"60f185dff4ce259d8c664c30c62d977d","spanId":"d5e9c5de5af8a6ac","spanName":"POST","msg":"REQUEST WILL FAIL: Missing read access to experiment id , or the experiment does not exist"}
{"level":50,"time":1755186171617,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","errors":"An IO error occurred: 'Generic MicrosoftAzure error: Error performing GET https://btstoragexl81cs.blob.core.windows.net/brainstore/brainstore/index/segments/2b91451f-6d40-4fab-837f-fe225ed89d91/tantivy/72ea8a16dbbe4a06b097055821df802d.term in 8.166066ms - HTTP error: error sending request'","traceId":"e49151fe1c51de5ee08a8a30dd9d1c42","spanId":"cc1c79d5d7bc4631","spanName":"POST","msg":"Failed to run BrainstoreQuery"}
{"level":40,"time":1755186171728,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"e49151fe1c51de5ee08a8a30dd9d1c42","spanId":"4e8f85dab212d00f","attributes":{"error.type":"Kt","error.status_code":200,"error_context.userId":"98c1d2c7-20d2-4ce6-8af5-8315fadb9cc5","error_context.userEmail":"<EMAIL>"},"events":[{"name":"exception","attributes":{"exception.type":"Error","exception.message":"Failed to run BrainstoreQuery: An IO error occurred: 'Generic MicrosoftAzure error: Error performing GET https://btstoragexl81cs.blob.core.windows.net/brainstore/brainstore/index/segments/2b91451f-6d40-4fab-837f-fe225ed89d91/tantivy/72ea8a16dbbe4a06b097055821df802d.term in 8.166066ms - HTTP error: error sending request'","exception.stacktrace":"Error: Failed to run BrainstoreQuery: An IO error occurred: 'Generic MicrosoftAzure error: Error performing GET https://btstoragexl81cs.blob.core.windows.net/brainstore/brainstore/index/segments/2b91451f-6d40-4fab-837f-fe225ed89d91/tantivy/72ea8a16dbbe4a06b097055821df802d.term in 8.166066ms - HTTP error: error sending request'\n    at SIo (/braintrust/api-ts/local/local.js:1462:38)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async IR (/braintrust/api-ts/local/local.js:2288:5803)\n    at async qbn (/braintrust/api-ts/local/local.js:2287:19284)\n    at async ZDm (/braintrust/api-ts/local/local.js:3614:14306)"},"time":[1755186171,728236815],"droppedAttributesCount":0}],"startTime":"2025-08-14T15:42:51.728Z","durationMs":"0.24","spanName":"error","msg":"error"}
{"level":50,"time":1755186171729,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","method":"POST","path":"/btql","statusCode":500,"error":"\nKt [Error]: Failed to run BrainstoreQuery: An IO error occurred: 'Generic MicrosoftAzure error: Error performing GET https://btstoragexl81cs.blob.core.windows.net/brainstore/brainstore/index/segments/2b91451f-6d40-4fab-837f-fe225ed89d91/tantivy/72ea8a16dbbe4a06b097055821df802d.term in 8.166066ms - HTTP error: error sending request'\n    at SIo (/braintrust/api-ts/local/local.js:1462:38)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async IR (/braintrust/api-ts/local/local.js:2288:5803)\n    at async qbn (/braintrust/api-ts/local/local.js:2287:19284)\n    at async ZDm (/braintrust/api-ts/local/local.js:3614:14306)","errorContext":{"userId":"98c1d2c7-20d2-4ce6-8af5-8315fadb9cc5","userEmail":"<EMAIL>","orgName":null},"traceId":"e49151fe1c51de5ee08a8a30dd9d1c42","spanId":"cc1c79d5d7bc4631","spanName":"POST","msg":"REQUEST WILL FAIL ( INTERNAL SERVER ERROR ): Failed to run BrainstoreQuery: An IO error occurred: 'Generic MicrosoftAzure error: Error performing GET https://btstoragexl81cs.blob.core.windows.net/brainstore/brainstore/index/segments/2b91451f-6d40-4fab-837f-fe225ed89d91/tantivy/72ea8a16dbbe4a06b097055821df802d.term in 8.166066ms - HTTP error: error sending request'"}
{"level":40,"time":1755186171729,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"e49151fe1c51de5ee08a8a30dd9d1c42","spanId":"cc1c79d5d7bc4631","attributes":{"http.url":"http://mangoeastus2prodcpu.inf5mi6t.com/btql","http.host":"mangoeastus2prodcpu.inf5mi6t.com","net.host.name":"mangoeastus2prodcpu.inf5mi6t.com","http.method":"POST","http.scheme":"http","http.client_ip":"************","http.target":"/btql","http.user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","http.request_content_length_uncompressed":1527,"http.flavor":"1.1","net.transport":"ip_tcp","commit":"23f4f06557fae20cfa21f97164e90a74a7000550","net.host.ip":"************","net.host.port":8000,"net.peer.ip":"*************","net.peer.port":43698,"http.status_code":500,"http.status_text":"INTERNAL SERVER ERROR"},"events":[],"startTime":"2025-08-14T15:42:50.717Z","durationMs":"1012.50","spanName":"POST","msg":"POST"}
{"level":50,"time":1755186463392,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","callerIdentifier":"[BrainstoreTS] [updateFrontierLoop] logs","error":{},"traceId":"ae8efe0cc449cc7020d9e294f90b963b","spanId":"fa72f1bc5282760d","spanName":"POST","msg":"Operation failed, continuing..."}
{"level":50,"time":1755186463392,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","callerIdentifier":"[BrainstoreTS] [updateFrontierLoop] logs2","error":{},"traceId":"ae8efe0cc449cc7020d9e294f90b963b","spanId":"fa72f1bc5282760d","spanName":"POST","msg":"Operation failed, continuing..."}
{"level":50,"time":1755186953955,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","errors":"Generic MicrosoftAzure error: Error performing GET https://btstoragexl81cs.blob.core.windows.net/brainstore/brainstore/wal/object-store-objects/3b5aa49d-e624-47dc-a963-c5d8f6a7ff95/experiment:7c80cd3a-fc18-48a2-bc5e-b837d998bdcd/1000195619705705291.b6b95374-8589-42f4-8f3d-295cc8c99495.jsonl in 710.484328ms - HTTP error: error sending request","traceId":"24939eccfc2669b5cd10d62b1d4c3c58","spanId":"40badc96025b6528","spanName":"POST","msg":"Failed to run BrainstoreQuery"}
{"level":40,"time":1755186954099,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"24939eccfc2669b5cd10d62b1d4c3c58","spanId":"acfa44b3ae28ed9e","attributes":{"error.type":"Kt","error.status_code":200,"error_context.userId":"bc6c6570-c896-46b0-912d-2f2937f213ba","error_context.userEmail":"<EMAIL>"},"events":[{"name":"exception","attributes":{"exception.type":"Error","exception.message":"Failed to run BrainstoreQuery: Generic MicrosoftAzure error: Error performing GET https://btstoragexl81cs.blob.core.windows.net/brainstore/brainstore/wal/object-store-objects/3b5aa49d-e624-47dc-a963-c5d8f6a7ff95/experiment:7c80cd3a-fc18-48a2-bc5e-b837d998bdcd/1000195619705705291.b6b95374-8589-42f4-8f3d-295cc8c99495.jsonl in 710.484328ms - HTTP error: error sending request","exception.stacktrace":"Error: Failed to run BrainstoreQuery: Generic MicrosoftAzure error: Error performing GET https://btstoragexl81cs.blob.core.windows.net/brainstore/brainstore/wal/object-store-objects/3b5aa49d-e624-47dc-a963-c5d8f6a7ff95/experiment:7c80cd3a-fc18-48a2-bc5e-b837d998bdcd/1000195619705705291.b6b95374-8589-42f4-8f3d-295cc8c99495.jsonl in 710.484328ms - HTTP error: error sending request\n    at SIo (/braintrust/api-ts/local/local.js:1462:38)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async IR (/braintrust/api-ts/local/local.js:2288:5803)\n    at async qbn (/braintrust/api-ts/local/local.js:2287:19284)\n    at async ZDm (/braintrust/api-ts/local/local.js:3614:14306)"},"time":[1755186954,99321415],"droppedAttributesCount":0}],"startTime":"2025-08-14T15:55:54.099Z","durationMs":"0.34","spanName":"error","msg":"error"}
{"level":50,"time":1755186954101,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","method":"POST","path":"/btql","statusCode":500,"error":"\nKt [Error]: Failed to run BrainstoreQuery: Generic MicrosoftAzure error: Error performing GET https://btstoragexl81cs.blob.core.windows.net/brainstore/brainstore/wal/object-store-objects/3b5aa49d-e624-47dc-a963-c5d8f6a7ff95/experiment:7c80cd3a-fc18-48a2-bc5e-b837d998bdcd/1000195619705705291.b6b95374-8589-42f4-8f3d-295cc8c99495.jsonl in 710.484328ms - HTTP error: error sending request\n    at SIo (/braintrust/api-ts/local/local.js:1462:38)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async IR (/braintrust/api-ts/local/local.js:2288:5803)\n    at async qbn (/braintrust/api-ts/local/local.js:2287:19284)\n    at async ZDm (/braintrust/api-ts/local/local.js:3614:14306)","errorContext":{"userId":"bc6c6570-c896-46b0-912d-2f2937f213ba","userEmail":"<EMAIL>","orgName":null},"traceId":"24939eccfc2669b5cd10d62b1d4c3c58","spanId":"40badc96025b6528","spanName":"POST","msg":"REQUEST WILL FAIL ( INTERNAL SERVER ERROR ): Failed to run BrainstoreQuery: Generic MicrosoftAzure error: Error performing GET https://btstoragexl81cs.blob.core.windows.net/brainstore/brainstore/wal/object-store-objects/3b5aa49d-e624-47dc-a963-c5d8f6a7ff95/experiment:7c80cd3a-fc18-48a2-bc5e-b837d998bdcd/1000195619705705291.b6b95374-8589-42f4-8f3d-295cc8c99495.jsonl in 710.484328ms - HTTP error: error sending request"}
{"level":40,"time":1755186954101,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"24939eccfc2669b5cd10d62b1d4c3c58","spanId":"40badc96025b6528","attributes":{"http.url":"http://mangoeastus2prodcpu.inf5mi6t.com/btql","http.host":"mangoeastus2prodcpu.inf5mi6t.com","net.host.name":"mangoeastus2prodcpu.inf5mi6t.com","http.method":"POST","http.scheme":"http","http.client_ip":"************","http.target":"/btql","http.user_agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","http.request_content_length_uncompressed":1528,"http.flavor":"1.1","net.transport":"ip_tcp","commit":"23f4f06557fae20cfa21f97164e90a74a7000550","net.host.ip":"************","net.host.port":8000,"net.peer.ip":"*************","net.peer.port":35180,"http.status_code":500,"http.status_text":"INTERNAL SERVER ERROR"},"events":[],"startTime":"2025-08-14T15:55:52.169Z","durationMs":"1931.84","spanName":"POST","msg":"POST"}
{"level":40,"time":1755187106879,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"1b0e870d64c60cc6dd315f4550eff1d2","spanId":"816f26921c8da5e6","attributes":{"http.request.method":"POST","http.request.method_original":"POST","url.full":"https://www.braintrust.dev/api/self/get_object_info","url.path":"/api/self/get_object_info","url.query":"","url.scheme":"https","server.address":"www.braintrust.dev","server.port":443,"network.peer.address":"***********","network.peer.port":443,"http.response.status_code":401,"http.response.header.content-length":220},"events":[],"startTime":"2025-08-14T15:58:26.404Z","durationMs":"474.41","spanName":"GET","msg":"POST"}
{"level":40,"time":1755187106985,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"1b0e870d64c60cc6dd315f4550eff1d2","spanId":"816f26921c8da5e6","attributes":{"http.request.method":"POST","http.request.method_original":"POST","url.full":"https://www.braintrust.dev/api/self/get_error_context","url.path":"/api/self/get_error_context","url.query":"","url.scheme":"https","server.address":"www.braintrust.dev","server.port":443,"network.peer.address":"***********","network.peer.port":443,"http.response.status_code":401,"http.response.header.content-length":220},"events":[],"startTime":"2025-08-14T15:58:26.939Z","durationMs":"46.10","spanName":"GET","msg":"POST"}
{"level":50,"time":1755187106987,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","error":"Xu [Error]: Failed to get error context:\nFailed to validate Clerk JWT: Error: JWT is expired. Expiry date: Wed, 13 Aug 2025 22:37:29 GMT, Current date: Thu, 14 Aug 2025 15:58:26 GMT. [timestamp=1755187106.959] [request_id=iad1::ns99g-1755187106922-40e26d171f38]\n    at t.getErrorContext (/braintrust/api-ts/local/local.js:1257:2999)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /braintrust/api-ts/local/local.js:3614:36874\n    at async /braintrust/api-ts/local/local.js:3614:36814 {\n  status: 401\n}","traceId":"1b0e870d64c60cc6dd315f4550eff1d2","spanId":"816f26921c8da5e6","spanName":"GET","msg":"Error details"}
{"level":40,"time":1755187106987,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"1b0e870d64c60cc6dd315f4550eff1d2","spanId":"60702c7de63754fb","attributes":{"error.type":"Xu","error.status_code":200},"events":[{"name":"exception","attributes":{"exception.type":"Error","exception.message":"Failed to get object info:\nFailed to validate Clerk JWT: Error: JWT is expired. Expiry date: Wed, 13 Aug 2025 22:37:29 GMT, Current date: Thu, 14 Aug 2025 15:58:26 GMT. [timestamp=1755187106.472] [request_id=iad1::hwnpk-1755187106418-66c34bef203c]","exception.stacktrace":"Error: Failed to get object info:\nFailed to validate Clerk JWT: Error: JWT is expired. Expiry date: Wed, 13 Aug 2025 22:37:29 GMT, Current date: Thu, 14 Aug 2025 15:58:26 GMT. [timestamp=1755187106.472] [request_id=iad1::hwnpk-1755187106418-66c34bef203c]\n    at t.checkAndGetMulti (/braintrust/api-ts/local/local.js:1256:13509)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async t.checkAndGet (/braintrust/api-ts/local/local.js:1256:12498)\n    at async DMn (/braintrust/api-ts/local/local.js:3309:7809)\n    at async lXa (/braintrust/api-ts/local/local.js:3614:6100)"},"time":[1755187106,987086515],"droppedAttributesCount":0}],"startTime":"2025-08-14T15:58:26.987Z","durationMs":"0.09","spanName":"error","msg":"error"}
{"level":30,"time":1755187106987,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","method":"GET","path":"/broadcast-key","statusCode":401,"error":"\nXu [Error]: Failed to get object info:\nFailed to validate Clerk JWT: Error: JWT is expired. Expiry date: Wed, 13 Aug 2025 22:37:29 GMT, Current date: Thu, 14 Aug 2025 15:58:26 GMT. [timestamp=1755187106.472] [request_id=iad1::hwnpk-1755187106418-66c34bef203c]\n    at t.checkAndGetMulti (/braintrust/api-ts/local/local.js:1256:13509)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async t.checkAndGet (/braintrust/api-ts/local/local.js:1256:12498)\n    at async DMn (/braintrust/api-ts/local/local.js:3309:7809)\n    at async lXa (/braintrust/api-ts/local/local.js:3614:6100) {\n  status: 401\n}","errorContext":{},"traceId":"1b0e870d64c60cc6dd315f4550eff1d2","spanId":"816f26921c8da5e6","spanName":"GET","msg":"REQUEST WILL FAIL: Failed to get object info:\nFailed to validate Clerk JWT: Error: JWT is expired. Expiry date: Wed, 13 Aug 2025 22:37:29 GMT, Current date: Thu, 14 Aug 2025 15:58:26 GMT. [timestamp=1755187106.472] [request_id=iad1::hwnpk-1755187106418-66c34bef203c]"}
{"level":40,"time":1755187107053,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"f4aae8f7685d750daefbaa64a0a69acc","spanId":"956e746c1a38c4b9","attributes":{"http.request.method":"POST","http.request.method_original":"POST","url.full":"https://www.braintrust.dev/api/self/get_object_info","url.path":"/api/self/get_object_info","url.query":"","url.scheme":"https","server.address":"www.braintrust.dev","server.port":443,"network.peer.address":"************","network.peer.port":443,"http.response.status_code":401,"http.response.header.content-length":220},"events":[],"startTime":"2025-08-14T15:58:26.964Z","durationMs":"88.87","spanName":"GET","msg":"POST"}
{"level":40,"time":1755187107115,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"1b0e870d64c60cc6dd315f4550eff1d2","spanId":"816f26921c8da5e6","attributes":{"http.request.method":"POST","http.request.method_original":"POST","url.full":"https://www.braintrust.dev/api/self/get_error_context","url.path":"/api/self/get_error_context","url.query":"","url.scheme":"https","server.address":"www.braintrust.dev","server.port":443,"network.peer.address":"***********","network.peer.port":443,"http.response.status_code":401,"http.response.header.content-length":220},"events":[],"startTime":"2025-08-14T15:58:27.058Z","durationMs":"57.18","spanName":"GET","msg":"POST"}
{"level":50,"time":1755187107115,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","error":"Xu [Error]: Failed to get error context:\nFailed to validate Clerk JWT: Error: JWT is expired. Expiry date: Wed, 13 Aug 2025 22:37:29 GMT, Current date: Thu, 14 Aug 2025 15:58:27 GMT. [timestamp=1755187107.089] [request_id=iad1::vx5g9-1755187107040-44415fb5e086]\n    at t.getErrorContext (/braintrust/api-ts/local/local.js:1257:2999)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /braintrust/api-ts/local/local.js:3614:36874\n    at async /braintrust/api-ts/local/local.js:3614:36814 {\n  status: 401\n}","traceId":"f4aae8f7685d750daefbaa64a0a69acc","spanId":"956e746c1a38c4b9","spanName":"GET","msg":"Error details"}
{"level":40,"time":1755187107115,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"f4aae8f7685d750daefbaa64a0a69acc","spanId":"7edaffc3b46623e0","attributes":{"error.type":"Xu","error.status_code":200},"events":[{"name":"exception","attributes":{"exception.type":"Error","exception.message":"Failed to get object info:\nFailed to validate Clerk JWT: Error: JWT is expired. Expiry date: Wed, 13 Aug 2025 22:37:29 GMT, Current date: Thu, 14 Aug 2025 15:58:27 GMT. [timestamp=1755187107.025] [request_id=iad1::mxwsh-1755187106982-a2219e7346fc]","exception.stacktrace":"Error: Failed to get object info:\nFailed to validate Clerk JWT: Error: JWT is expired. Expiry date: Wed, 13 Aug 2025 22:37:29 GMT, Current date: Thu, 14 Aug 2025 15:58:27 GMT. [timestamp=1755187107.025] [request_id=iad1::mxwsh-1755187106982-a2219e7346fc]\n    at t.checkAndGetMulti (/braintrust/api-ts/local/local.js:1256:13509)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async t.checkAndGet (/braintrust/api-ts/local/local.js:1256:12498)\n    at async DMn (/braintrust/api-ts/local/local.js:3309:7809)\n    at async lXa (/braintrust/api-ts/local/local.js:3614:6100)"},"time":[1755187107,115030288],"droppedAttributesCount":0}],"startTime":"2025-08-14T15:58:27.115Z","durationMs":"0.03","spanName":"error","msg":"error"}
{"level":30,"time":1755187107115,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","method":"GET","path":"/broadcast-key","statusCode":401,"error":"\nXu [Error]: Failed to get object info:\nFailed to validate Clerk JWT: Error: JWT is expired. Expiry date: Wed, 13 Aug 2025 22:37:29 GMT, Current date: Thu, 14 Aug 2025 15:58:27 GMT. [timestamp=1755187107.025] [request_id=iad1::mxwsh-1755187106982-a2219e7346fc]\n    at t.checkAndGetMulti (/braintrust/api-ts/local/local.js:1256:13509)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async t.checkAndGet (/braintrust/api-ts/local/local.js:1256:12498)\n    at async DMn (/braintrust/api-ts/local/local.js:3309:7809)\n    at async lXa (/braintrust/api-ts/local/local.js:3614:6100) {\n  status: 401\n}","errorContext":{},"traceId":"f4aae8f7685d750daefbaa64a0a69acc","spanId":"956e746c1a38c4b9","spanName":"GET","msg":"REQUEST WILL FAIL: Failed to get object info:\nFailed to validate Clerk JWT: Error: JWT is expired. Expiry date: Wed, 13 Aug 2025 22:37:29 GMT, Current date: Thu, 14 Aug 2025 15:58:27 GMT. [timestamp=1755187107.025] [request_id=iad1::mxwsh-1755187106982-a2219e7346fc]"}
{"level":40,"time":1755187107764,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"1b0e870d64c60cc6dd315f4550eff1d2","spanId":"816f26921c8da5e6","attributes":{"http.request.method":"POST","http.request.method_original":"POST","url.full":"https://www.braintrust.dev/api/self/get_object_info","url.path":"/api/self/get_object_info","url.query":"","url.scheme":"https","server.address":"www.braintrust.dev","server.port":443,"network.peer.address":"***********","network.peer.port":443,"http.response.status_code":401,"http.response.header.content-length":220},"events":[],"startTime":"2025-08-14T15:58:27.331Z","durationMs":"432.96","spanName":"GET","msg":"POST"}
{"level":40,"time":1755187107837,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"1b0e870d64c60cc6dd315f4550eff1d2","spanId":"816f26921c8da5e6","attributes":{"http.request.method":"POST","http.request.method_original":"POST","url.full":"https://www.braintrust.dev/api/self/get_error_context","url.path":"/api/self/get_error_context","url.query":"","url.scheme":"https","server.address":"www.braintrust.dev","server.port":443,"network.peer.address":"***********","network.peer.port":443,"http.response.status_code":401,"http.response.header.content-length":220},"events":[],"startTime":"2025-08-14T15:58:27.783Z","durationMs":"54.57","spanName":"GET","msg":"POST"}
{"level":50,"time":1755187107837,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","error":"Xu [Error]: Failed to get error context:\nFailed to validate Clerk JWT: Error: JWT is expired. Expiry date: Wed, 13 Aug 2025 22:37:29 GMT, Current date: Thu, 14 Aug 2025 15:58:27 GMT. [timestamp=1755187107.811] [request_id=iad1::b85jz-1755187107765-17054c42fd2c]\n    at t.getErrorContext (/braintrust/api-ts/local/local.js:1257:2999)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /braintrust/api-ts/local/local.js:3614:36874\n    at async /braintrust/api-ts/local/local.js:3614:36814 {\n  status: 401\n}","traceId":"995eb44773c19fa9c4e2145c04669b66","spanId":"84a4070c3e076e7b","spanName":"GET","msg":"Error details"}
{"level":40,"time":1755187107838,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"995eb44773c19fa9c4e2145c04669b66","spanId":"ffaeaef6d5df02a0","attributes":{"error.type":"Xu","error.status_code":200},"events":[{"name":"exception","attributes":{"exception.type":"Error","exception.message":"Failed to get object info:\nFailed to validate Clerk JWT: Error: JWT is expired. Expiry date: Wed, 13 Aug 2025 22:37:29 GMT, Current date: Thu, 14 Aug 2025 15:58:27 GMT. [timestamp=1755187107.675] [request_id=iad1::b85jz-1755187107314-f31c7fba8b9c]","exception.stacktrace":"Error: Failed to get object info:\nFailed to validate Clerk JWT: Error: JWT is expired. Expiry date: Wed, 13 Aug 2025 22:37:29 GMT, Current date: Thu, 14 Aug 2025 15:58:27 GMT. [timestamp=1755187107.675] [request_id=iad1::b85jz-1755187107314-f31c7fba8b9c]\n    at t.checkAndGetMulti (/braintrust/api-ts/local/local.js:1256:13509)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async t.checkAndGet (/braintrust/api-ts/local/local.js:1256:12498)\n    at async DMn (/braintrust/api-ts/local/local.js:3309:7809)\n    at async lXa (/braintrust/api-ts/local/local.js:3614:6100)"},"time":[1755187107,837056763],"droppedAttributesCount":0}],"startTime":"2025-08-14T15:58:27.837Z","durationMs":"0.06","spanName":"error","msg":"error"}
{"level":30,"time":1755187107838,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","method":"GET","path":"/broadcast-key","statusCode":401,"error":"\nXu [Error]: Failed to get object info:\nFailed to validate Clerk JWT: Error: JWT is expired. Expiry date: Wed, 13 Aug 2025 22:37:29 GMT, Current date: Thu, 14 Aug 2025 15:58:27 GMT. [timestamp=1755187107.675] [request_id=iad1::b85jz-1755187107314-f31c7fba8b9c]\n    at t.checkAndGetMulti (/braintrust/api-ts/local/local.js:1256:13509)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async t.checkAndGet (/braintrust/api-ts/local/local.js:1256:12498)\n    at async DMn (/braintrust/api-ts/local/local.js:3309:7809)\n    at async lXa (/braintrust/api-ts/local/local.js:3614:6100) {\n  status: 401\n}","errorContext":{},"traceId":"995eb44773c19fa9c4e2145c04669b66","spanId":"84a4070c3e076e7b","spanName":"GET","msg":"REQUEST WILL FAIL: Failed to get object info:\nFailed to validate Clerk JWT: Error: JWT is expired. Expiry date: Wed, 13 Aug 2025 22:37:29 GMT, Current date: Thu, 14 Aug 2025 15:58:27 GMT. [timestamp=1755187107.675] [request_id=iad1::b85jz-1755187107314-f31c7fba8b9c]"}
{"level":40,"time":1755187577015,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","mergeIds":"{\"objectType\":\"experiment\",\"objectId\":\"99061f7c-cb5b-4b16-92e6-5911d122bb4d\",\"id\":\"a6e81c99-6b67-4d8a-b130-fd72d6878596\"}, {\"objectType\":\"experiment\",\"objectId\":\"99061f7c-cb5b-4b16-92e6-5911d122bb4d\",\"id\":\"c0dc212b-cba5-42b5-a0bf-c511fb539db5\"}, {\"objectType\":\"experiment\",\"objectId\":\"99061f7c-cb5b-4b16-92e6-5911d122bb4d\",\"id\":\"abb76d27-c92d-471d-b522-8d13a0b25dd2\"}, {\"objectType\":\"experiment\",\"objectId\":\"99061f7c-cb5b-4b16-92e6-5911d122bb4d\",\"id\":\"92cbb2aa-07ac-4b11-a039-9aae494828dc\"}, {\"objectType\":\"experiment\",\"objectId\":\"99061f7c-cb5b-4b16-92e6-5911d122bb4d\",\"id\":\"b998853f-2b87-41e3-89e1-2872f53f591a\"}, {\"objectType\":\"experiment\",\"objectId\":\"99061f7c-cb5b-4b16-92e6-5911d122bb4d\",\"id\":\"79858b7f-4451-4208-817f-08ad05f31e3a\"}, {\"objectType\":\"experiment\",\"objectId\":\"99061f7c-cb5b-4b16-92e6-5911d122bb4d\",\"id\":\"49a048fe-57de-4b4c-ad18-512df73c4c9e\"}","traceId":"d2d7048e06eb0ac0783d7558d026eb5d","spanId":"dea9e2ede5cf81d6","spanName":"write rows to pg","msg":"Merge advisory locks timeout"}
{"level":50,"time":1755187577015,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","error":"Timed out waiting for merge advisory locks","orgIds":["6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78","6e2a32b0-8f23-4cf0-abb6-fcd427de3c78"],"traceId":"d2d7048e06eb0ac0783d7558d026eb5d","spanId":"21cb1774b42ed256","spanName":"POST","msg":"Query timed out"}
{"level":40,"time":1755187577016,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"d2d7048e06eb0ac0783d7558d026eb5d","spanId":"21cb1774b42ed256","spanName":"POST","msg":"Forcibly terminating process at pid 460007"}
{"level":40,"time":1755187577019,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","traceId":"d2d7048e06eb0ac0783d7558d026eb5d","spanId":"4afed049520cc648","attributes":{"error.type":"Xu","error.status_code":200,"error_context.userId":"d30b92da-a28c-46be-b489-996fa396c82b","error_context.userEmail":"<EMAIL>","error_context.orgName":"Microsoft Copilot PME"},"events":[{"name":"exception","attributes":{"exception.type":"Error","exception.message":"Too many concurrent insert requests","exception.stacktrace":"Error: Too many concurrent insert requests\n    at eDm (/braintrust/api-ts/local/local.js:3526:994)\n    at async j2m (/braintrust/api-ts/local/local.js:3364:8609)\n    at async lst (/braintrust/api-ts/local/local.js:3614:6279)\n    at async Q4e (/braintrust/api-ts/local/local.js:3614:6440)"},"time":[1755187577,19223293],"droppedAttributesCount":0}],"startTime":"2025-08-14T16:06:17.019Z","durationMs":"0.22","spanName":"error","msg":"error"}
{"level":30,"time":1755187577020,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","method":"POST","path":"/logs3","statusCode":429,"error":"\nXu [Error]: Too many concurrent insert requests\n    at eDm (/braintrust/api-ts/local/local.js:3526:994)\n    at async j2m (/braintrust/api-ts/local/local.js:3364:8609)\n    at async lst (/braintrust/api-ts/local/local.js:3614:6279)\n    at async Q4e (/braintrust/api-ts/local/local.js:3614:6440) {\n  status: 429\n}","errorContext":{"userId":"d30b92da-a28c-46be-b489-996fa396c82b","userEmail":"<EMAIL>","orgName":"Microsoft Copilot PME"},"traceId":"d2d7048e06eb0ac0783d7558d026eb5d","spanId":"21cb1774b42ed256","spanName":"POST","msg":"REQUEST WILL FAIL: Too many concurrent insert requests"}
{"level":50,"time":1755187666997,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","callerIdentifier":"[BrainstoreTS] [updateFrontierLoop] logs2","error":{},"traceId":"11271c8f4481549c0379beadcdd0e514","spanId":"4d58b8261bd03966","spanName":"POST","msg":"Operation failed, continuing..."}
{"level":50,"time":1755187666997,"pid":8,"hostname":"braintrust-api-7f8dd5fc6-9fsph","name":"braintrust-api","callerIdentifier":"[BrainstoreTS] [updateFrontierLoop] logs","error":{},"traceId":"11271c8f4481549c0379beadcdd0e514","spanId":"4d58b8261bd03966","spanName":"POST","msg":"Operation failed, continuing..."}
